import React, { useState, useMemo } from 'react';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    ToggleButton, FormControlLabel, Drawer, Chip, FormControl, InputLabel, Select, MenuItem,
    Tabs, Tab, Grid, Dialog, DialogContent, DialogActions
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, People, CheckCircle, Cancel, WarningAmber, ShowChart, PieChart, DonutLarge,
    Settings, Save, Close, Upload, CloudUpload, AccountBalance, Assessment,
    Description, Download, Print, AttachMoney, Build, Storage, School, IntegrationInstructions
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';
import { 
    ProductSupportCard, 
    ProductSupportCompactCard, 
    ProductSupportListItem, 
    ProductSupportTable, 
    ProductSupportDialog, 
    initialProductSupports, 
    ALL_COLUMNS 
} from './ProductSupport';

const Support = () => {
    const [supports, setSupports] = useState(initialProductSupports);
    const [filteredSupports, setFilteredSupports] = useState(initialProductSupports);
    const [selectedId, setSelectedId] = useState(null);
    const [selectedIds, setSelectedIds] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [sortColumn, setSortColumn] = useState('createdDate');
    const [sortDirection, setSortDirection] = useState('desc');
    const [viewMode, setViewMode] = useState('table');
    const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(col => col.key));
    const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
    
    // Dialog states
    const [dialogOpen, setDialogOpen] = useState(false);
    const [dialogMode, setDialogMode] = useState('add'); // 'add', 'edit', 'view'
    const [dialogSupport, setDialogSupport] = useState(null);
    
    // Success message state
    const [successMessage, setSuccessMessage] = useState('');

    // Filter and search logic
    const handleSearch = (term) => {
        setSearchTerm(term);
        if (!term.trim()) {
            setFilteredSupports(supports);
            return;
        }

        const filtered = supports.filter(support =>
            Object.values(support).some(value =>
                value && value.toString().toLowerCase().includes(term.toLowerCase())
            )
        );
        setFilteredSupports(filtered);
    };

    // Sorting logic
    const handleSort = (column) => {
        const isAsc = sortColumn === column && sortDirection === 'asc';
        const direction = isAsc ? 'desc' : 'asc';
        setSortColumn(column);
        setSortDirection(direction);

        const sorted = [...filteredSupports].sort((a, b) => {
            if (column === 'costRate') {
                return direction === 'asc' ? a[column] - b[column] : b[column] - a[column];
            }
            if (column === 'createdDate') {
                return direction === 'asc' 
                    ? new Date(a[column]) - new Date(b[column])
                    : new Date(b[column]) - new Date(a[column]);
            }
            const aVal = a[column]?.toString().toLowerCase() || '';
            const bVal = b[column]?.toString().toLowerCase() || '';
            return direction === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
        });

        setFilteredSupports(sorted);
    };

    // Selection handlers
    const handleSelectAll = (event) => {
        if (event.target.checked) {
            setSelectedIds(filteredSupports.map(support => support.id));
        } else {
            setSelectedIds([]);
        }
    };

    const handleSelectOne = (id) => {
        setSelectedIds(prev =>
            prev.includes(id) ? prev.filter(selectedId => selectedId !== id) : [...prev, id]
        );
    };

    const handleRowClick = (support) => {
        setSelectedId(support.id);
    };

    // Dialog handlers
    const handleOpenDialog = (mode, support = null) => {
        setDialogMode(mode);
        setDialogSupport(support);
        setDialogOpen(true);
    };

    const handleCloseDialog = () => {
        setDialogOpen(false);
        setDialogMode('add');
        setDialogSupport(null);
    };

    const handleSaveDialog = (supportData) => {
        if (dialogMode === 'add') {
            const newSupport = {
                ...supportData,
                id: `ps-${Date.now()}`,
                createdDate: new Date().toISOString(),
                createdBy: 'Current User'
            };
            setSupports(prev => [...prev, newSupport]);
            setFilteredSupports(prev => [...prev, newSupport]);
            setSuccessMessage('Product Support added successfully!');
        } else if (dialogMode === 'edit') {
            setSupports(prev => prev.map(s => s.id === supportData.id ? supportData : s));
            setFilteredSupports(prev => prev.map(s => s.id === supportData.id ? supportData : s));
            setSuccessMessage('Product Support updated successfully!');
        }
        handleCloseDialog();
        
        // Clear success message after 3 seconds
        setTimeout(() => setSuccessMessage(''), 3000);
    };

    // Action handlers
    const handleView = (support) => {
        handleOpenDialog('view', support);
    };

    const handleEdit = (support) => {
        handleOpenDialog('edit', support);
    };

    const handleDelete = (ids) => {
        if (window.confirm(`Are you sure you want to delete ${ids.length} product support(s)?`)) {
            setSupports(prev => prev.filter(support => !ids.includes(support.id)));
            setFilteredSupports(prev => prev.filter(support => !ids.includes(support.id)));
            setSelectedIds([]);
            setSelectedId(null);
        }
    };

    const handleAdd = () => {
        handleOpenDialog('add');
    };

    // Summary calculations
    const summaryData = useMemo(() => {
        const totalSupports = filteredSupports.length;
        const totalCost = filteredSupports.reduce((sum, support) => sum + support.costRate, 0);
        const activeSupports = filteredSupports.filter(s => s.status === 'Active').length;
        const implementationSupports = filteredSupports.filter(s => s.category === 'Implementation Cost/Rate').length;
        const dataMigrationSupports = filteredSupports.filter(s => s.category === 'Data Migration').length;
        const integrationSupports = filteredSupports.filter(s => s.category === 'Interface Integration').length;
        const trainingSupports = filteredSupports.filter(s => s.category === 'Training Cost/Rate').length;

        return {
            totalSupports,
            totalCost,
            activeSupports,
            implementationSupports,
            dataMigrationSupports,
            integrationSupports,
            trainingSupports,
            averageCost: totalSupports > 0 ? totalCost / totalSupports : 0
        };
    }, [filteredSupports]);

    const renderContent = () => (
        <AdminComponents.ViewContainer>
            {filteredSupports.length > 0 ? (
                <>
                    {viewMode === 'grid' && (
                        <AdminComponents.GridView>
                            {filteredSupports.map(support => (
                                <ProductSupportCard
                                    key={support.id}
                                    support={support}
                                    isSelected={selectedId === support.id}
                                    onSelect={handleRowClick}
                                    isChecked={selectedIds.includes(support.id)}
                                    onDelete={handleDelete}
                                    onEdit={handleEdit}
                                    onView={handleView}
                                />
                            ))}
                        </AdminComponents.GridView>
                    )}
                    {viewMode === 'compact' && (
                        <AdminComponents.CompactView>
                            {filteredSupports.map(support => (
                                <ProductSupportCompactCard
                                    key={support.id}
                                    support={support}
                                    isSelected={selectedId === support.id}
                                    onSelect={handleRowClick}
                                    isChecked={selectedIds.includes(support.id)}
                                    onDelete={handleDelete}
                                    onEdit={handleEdit}
                                    onView={handleView}
                                />
                            ))}
                        </AdminComponents.CompactView>
                    )}
                    {viewMode === 'list' && (
                        <AdminComponents.ListView>
                            {filteredSupports.map(support => (
                                <ProductSupportListItem
                                    key={support.id}
                                    support={support}
                                    isSelected={selectedId === support.id}
                                    onSelect={handleRowClick}
                                    isChecked={selectedIds.includes(support.id)}
                                    onDelete={handleDelete}
                                    onEdit={handleEdit}
                                    onView={handleView}
                                />
                            ))}
                        </AdminComponents.ListView>
                    )}
                    {viewMode === 'table' && (
                        <ProductSupportTable
                            supports={filteredSupports}
                            onRowClick={handleRowClick}
                            onHeaderClick={handleSort}
                            sortColumn={sortColumn}
                            sortDirection={sortDirection}
                            selectedId={selectedId}
                            selectedIds={selectedIds}
                            onSelectAll={handleSelectAll}
                            onSelectOne={handleSelectOne}
                            columnOrder={columnOrder}
                            setColumnOrder={setColumnOrder}
                            onDelete={handleDelete}
                            onEdit={handleEdit}
                            onView={handleView}
                        />
                    )}
                </>
            ) : (
                <AdminComponents.CenteredMessage component={Paper}>
                    <AdminComponents.LargeIcon color="disabled" />
                    <Typography variant="h6">No Product Support Found</Typography>
                    <Typography color="text.secondary">
                        {searchTerm ? 'No product support matches your search criteria.' : 'No product support available.'}
                    </Typography>
                    <Button variant="contained" startIcon={<Add />} onClick={handleAdd}>
                        Add New Product Support
                    </Button>
                </AdminComponents.CenteredMessage>
            )}
        </AdminComponents.ViewContainer>
    );

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={filterDrawerOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={filterDrawerOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                {successMessage && (
                                    <Box sx={{ mb: 2 }}>
                                        <Typography 
                                            variant="body2" 
                                            sx={{ 
                                                color: 'var(--success-main)', 
                                                backgroundColor: 'var(--success-light)', 
                                                padding: '8px 16px', 
                                                borderRadius: 'var(--radius-md)',
                                                border: '1px solid var(--success-main)'
                                            }}
                                        >
                                            {successMessage}
                                        </Typography>
                                    </Box>
                                )}
                                <AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="total">
                                            <Build />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{summaryData.implementationSupports}</Typography>
                                            <Typography variant="body2">Implementation Cost/Rate</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="active">
                                            <Storage />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{summaryData.dataMigrationSupports}</Typography>
                                            <Typography variant="body2">Data Migration</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="active">
                                            <IntegrationInstructions />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{summaryData.integrationSupports}</Typography>
                                            <Typography variant="body2">Interface Integration</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="inactive">
                                            <School />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{summaryData.trainingSupports}</Typography>
                                            <Typography variant="body2">Training Cost/Rate</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                                    <Button
                                        variant="contained"
                                        startIcon={<Add />}
                                        onClick={handleAdd}
                                    >
                                        Add Product Support
                                    </Button>
                                    {selectedIds.length > 0 && (
                                        <Button
                                            variant="outlined"
                                            color="error"
                                            startIcon={<Delete />}
                                            onClick={() => handleDelete(selectedIds)}
                                        >
                                            Delete Selected ({selectedIds.length})
                                        </Button>
                                    )}
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>

                        <AdminComponents.ControlsSection>
                            <AdminComponents.ControlsGroup>
                                <TextField
                                    variant="outlined"
                                    size="small"
                                    placeholder="Search product support..."
                                    value={searchTerm}
                                    onChange={(e) => handleSearch(e.target.value)}
                                    slotProps={{ input: { startAdornment: <Search color="disabled" /> } }}
                                />
                                <Button
                                    variant="outlined"
                                    startIcon={<FilterAlt />}
                                    onClick={() => setFilterDrawerOpen(true)}
                                >
                                    Advanced Search
                                </Button>
                            </AdminComponents.ControlsGroup>

                            <AdminComponents.ControlsGroup>
                                <AdminComponents.StyledToggleButtonGroup
                                    size="small"
                                    value={viewMode}
                                    exclusive
                                    onChange={(_, newView) => newView && setViewMode(newView)}
                                >
                                    <ToggleButton value="grid" title="Card View">
                                        <ViewModule />Card
                                    </ToggleButton>
                                    <ToggleButton value="compact" title="Compact View">
                                        <Apps />Compact
                                    </ToggleButton>
                                    <ToggleButton value="list" title="List View">
                                        <ViewList />List
                                    </ToggleButton>
                                    <ToggleButton value="table" title="Table View">
                                        <GridView />Table
                                    </ToggleButton>
                                </AdminComponents.StyledToggleButtonGroup>
                            </AdminComponents.ControlsGroup>
                        </AdminComponents.ControlsSection>

                        <AdminComponents.ContentBody>
                            <AdminComponents.MainLeftPane>
                                {renderContent()}
                            </AdminComponents.MainLeftPane>
                        </AdminComponents.ContentBody>
                    </AdminComponents.MainContentArea>
                </AdminComponents.AppBody>

                {/* Filter Drawer */}
                <Drawer
                    variant="persistent"
                    anchor="right"
                    open={filterDrawerOpen}
                >
                    <AdminComponents.SidebarContainer>
                        <AdminComponents.SidebarHeader>
                            <Typography variant="h6">Advanced Search</Typography>
                            <IconButton onClick={() => setFilterDrawerOpen(false)}>
                                <Close />
                            </IconButton>
                        </AdminComponents.SidebarHeader>
                        <AdminComponents.SidebarContent>
                            <AdminComponents.SidebarSection>
                                <Typography variant="body2" color="text.secondary">
                                    Advanced filtering options will be available here.
                                </Typography>
                            </AdminComponents.SidebarSection>
                        </AdminComponents.SidebarContent>
                        <AdminComponents.SidebarFooter>
                            <Button variant="contained" fullWidth onClick={() => setFilterDrawerOpen(false)}>
                                Close
                            </Button>
                        </AdminComponents.SidebarFooter>
                    </AdminComponents.SidebarContainer>
                </Drawer>

                {/* Product Support Dialog */}
                <ProductSupportDialog
                    open={dialogOpen}
                    onClose={handleCloseDialog}
                    supportData={dialogSupport}
                    mode={dialogMode}
                    onSave={handleSaveDialog}
                />
            </AdminComponents.AppContainer>
        </ThemeProvider>
    );
};

export default Support;
