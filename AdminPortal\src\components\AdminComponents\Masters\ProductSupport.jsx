import React, { useState, useMemo } from 'react';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    ToggleButton, FormControlLabel, Drawer, Chip, FormControl, InputLabel, Select, MenuItem,
    Tabs, Tab, Grid, Dialog, DialogContent, DialogActions
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, People, CheckCircle, Cancel, WarningAmber, ShowChart, PieChart, DonutLarge,
    Settings, Save, Close, Upload, CloudUpload, AccountBalance, Assessment,
    Description, Download, Print, AttachMoney, Build, Storage, School, IntegrationInstructions
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';

// Mock product support data with the four categories
const initialProductSupports = [
  {
    id: 'ps-001',
    supportId: 'PS-2025-001',
    customerName: 'Acme Global Services',
    productName: 'HCL Commerce Suite',
    category: 'Implementation Cost/Rate',
    description: 'Complete implementation of HCL Commerce Suite with custom configurations',
    costRate: 150000,
    currency: 'INR',
    duration: '3 months',
    status: 'Active',
    startDate: '2025-01-15',
    endDate: '2025-04-15',
    assignedTo: 'Implementation Team A',
    priority: 'High',
    region: 'North America',
    createdBy: 'John Smith',
    createdDate: '2025-01-10T10:30:00Z'
  },
  {
    id: 'ps-002',
    supportId: 'PS-2025-002',
    customerName: 'TechGlobal Solutions',
    productName: 'HCL Digital Experience',
    category: 'Data Migration',
    description: 'Migration of legacy data to HCL Digital Experience platform',
    costRate: 85000,
    currency: 'INR',
    duration: '6 weeks',
    status: 'In Progress',
    startDate: '2025-02-01',
    endDate: '2025-03-15',
    assignedTo: 'Data Migration Team',
    priority: 'Medium',
    region: 'Europe',
    createdBy: 'Sarah Johnson',
    createdDate: '2025-01-25T14:45:00Z'
  },
  {
    id: 'ps-003',
    supportId: 'PS-2025-003',
    customerName: 'Global Manufacturing Ltd',
    productName: 'HCL Manufacturing Suite',
    category: 'Interface Integration',
    description: 'Integration with existing ERP and CRM systems',
    costRate: 120000,
    currency: 'INR',
    duration: '8 weeks',
    status: 'Planning',
    startDate: '2025-03-01',
    endDate: '2025-04-26',
    assignedTo: 'Integration Team B',
    priority: 'High',
    region: 'Asia',
    createdBy: 'Michael Chen',
    createdDate: '2025-02-15T09:00:00Z'
  },
  {
    id: 'ps-004',
    supportId: 'PS-2025-004',
    customerName: 'Energy Solutions Inc',
    productName: 'HCL Energy Suite',
    category: 'Training Cost/Rate',
    description: 'Comprehensive user training and certification program',
    costRate: 45000,
    currency: 'INR',
    duration: '4 weeks',
    status: 'Scheduled',
    startDate: '2025-04-01',
    endDate: '2025-04-30',
    assignedTo: 'Training Team',
    priority: 'Medium',
    region: 'Europe',
    createdBy: 'Lisa Anderson',
    createdDate: '2025-03-10T13:00:00Z'
  },
  {
    id: 'ps-005',
    supportId: 'PS-2025-005',
    customerName: 'Logistics Corp APAC',
    productName: 'HCL Logistics Suite',
    category: 'Implementation Cost/Rate',
    description: 'Full implementation with custom workflow automation',
    costRate: 200000,
    currency: 'INR',
    duration: '4 months',
    status: 'Active',
    startDate: '2025-02-15',
    endDate: '2025-06-15',
    assignedTo: 'Implementation Team C',
    priority: 'High',
    region: 'Asia',
    createdBy: 'David Kim',
    createdDate: '2025-02-10T15:30:00Z'
  }
];

const ALL_COLUMNS = [
  { key: 'supportId', label: 'Support ID', type: 'string', groupable: true },
  { key: 'customerName', label: 'Customer Name', type: 'string', groupable: true },
  { key: 'productName', label: 'Product Name', type: 'string', groupable: true },
  { key: 'category', label: 'Category', type: 'string', groupable: true },
  { key: 'costRate', label: 'Cost/Rate (INR)', type: 'number', groupable: false },
  { key: 'duration', label: 'Duration', type: 'string', groupable: true },
  { key: 'status', label: 'Status', type: 'string', groupable: true },
  { key: 'priority', label: 'Priority', type: 'string', groupable: true },
];

const ActionButtons = ({ support, onView, onEdit, onDelete }) => (
    <Box onClick={e => e.stopPropagation()}>
        <IconButton size="small" onClick={() => onView(support)} title="View Details">
            <Visibility fontSize="small" />
        </IconButton>
        <IconButton size="small" onClick={() => onEdit(support)} title="Edit">
            <Edit fontSize="small" />
        </IconButton>
        <IconButton size="small" color="error" onClick={() => onDelete([support.id])} title="Delete">
            <Delete fontSize="small" />
        </IconButton>
    </Box>
);

const ProductSupportCard = ({ support, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
  <AdminComponents.CardBase isSelected={isSelected}>
    <Checkbox
      className="card-checkbox"
      checked={isChecked}
      onChange={() => onSelect(support.id)}
      onClick={e => e.stopPropagation()}
    />
    <AdminComponents.CardActionContainer>
      <ActionButtons support={support} onView={onView} onEdit={onEdit} onDelete={onDelete} />
    </AdminComponents.CardActionContainer>
    <AdminComponents.PaddedCardContent>
      <Typography variant="h6" component="div" noWrap>{support.supportId}</Typography>
      <Typography color="text.secondary" noWrap gutterBottom>{support.customerName}</Typography>
      <AdminComponents.StatusBadge
        ownerState={{ status: support.status }}
        label={support.status}
        size="small"
      />
      <AdminComponents.CardDivider />
      <AdminComponents.CardDetailsGrid>
        <AdminComponents.CardDetailLabel variant="body2">Category:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>{support.category}</Typography>
        <AdminComponents.CardDetailLabel variant="body2">Product:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>{support.productName}</Typography>
        <AdminComponents.CardDetailLabel variant="body2">Cost/Rate:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>
          {support.costRate.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
        </Typography>
        <AdminComponents.CardDetailLabel variant="body2">Duration:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>{support.duration}</Typography>
      </AdminComponents.CardDetailsGrid>
    </AdminComponents.PaddedCardContent>
  </AdminComponents.CardBase>
);

const ProductSupportCompactCard = ({ support, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
  <AdminComponents.CardBase isSelected={isSelected}>
    <Checkbox
      className="card-checkbox"
      checked={isChecked}
      onChange={() => onSelect(support.id)}
      onClick={e => e.stopPropagation()}
    />
    <AdminComponents.CardActionContainer>
      <ActionButtons support={support} onView={onView} onEdit={onEdit} onDelete={onDelete} />
    </AdminComponents.CardActionContainer>
    <AdminComponents.CompactCardContent>
      <div>
        <Typography variant="subtitle1" fontWeight="bold" noWrap>{support.supportId}</Typography>
        <Typography variant="caption" color="text.secondary">{support.customerName}</Typography>
      </div>
      <AdminComponents.CompactCardFooter>
        <Typography variant="body2" fontWeight="500">
          {support.costRate.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
        </Typography>
        <AdminComponents.StatusBadge
          ownerState={{ status: support.status }}
          label={support.status}
          size="small"
        />
      </AdminComponents.CompactCardFooter>
    </AdminComponents.CompactCardContent>
  </AdminComponents.CardBase>
);

const ProductSupportListItem = ({ support, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
  <AdminComponents.ListItemCard isSelected={isSelected}>
    <AdminComponents.ListItemGrid>
      <Checkbox
        checked={isChecked}
        onChange={() => onSelect(support.id)}
        onClick={e => e.stopPropagation()}
      />
      <Box>
        <Typography fontWeight="bold">{support.supportId}</Typography>
        <Typography variant="body2" color="text.secondary">{support.customerName}</Typography>
      </Box>
      <Typography variant="body2">{support.category}</Typography>
      <Typography variant="body2">{support.productName}</Typography>
      <Typography variant="body2">
        {support.costRate.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
      </Typography>
      <Typography variant="body2">{support.duration}</Typography>
      <AdminComponents.StatusBadge
        ownerState={{ status: support.status }}
        label={support.status}
        size="small"
      />
      <AdminComponents.ListItemActions>
        <ActionButtons support={support} onView={onView} onEdit={onEdit} onDelete={onDelete} />
      </AdminComponents.ListItemActions>
    </AdminComponents.ListItemGrid>
  </AdminComponents.ListItemCard>
);

const ProductSupportTable = ({
  supports,
  onRowClick,
  onHeaderClick,
  sortColumn,
  sortDirection,
  selectedId,
  selectedIds,
  onSelectAll,
  onSelectOne,
  columnOrder,
  setColumnOrder,
  onDelete,
  onEdit,
  onView
}) => {
    const dragItemIndex = React.useRef(null);
    const dragOverItemIndex = React.useRef(null);

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };

    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox">
                            <Checkbox
                                indeterminate={selectedIds.length > 0 && selectedIds.length < supports.length}
                                checked={supports.length > 0 && selectedIds.length === supports.length}
                                onChange={onSelectAll}
                            />
                        </TableCell>
                        {columnOrder.map((colKey, idx) => (
                            <TableCell
                                key={colKey}
                                draggable
                                onDragStart={() => { dragItemIndex.current = idx; }}
                                onDragEnter={() => { dragOverItemIndex.current = idx; }}
                                onDragEnd={handleDrop}
                                onDragOver={e => e.preventDefault()}
                                style={{ cursor: 'move' }}
                            >
                                <TableSortLabel
                                    active={sortColumn === colKey}
                                    direction={sortDirection}
                                    onClick={() => onHeaderClick(colKey)}
                                >
                                    {ALL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </TableCell>
                        ))}
                        <TableCell align="center">Actions</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {supports.map(support => (
                        <TableRow
                            key={support.id}
                            hover
                            selected={selectedId === support.id}
                            onClick={() => onRowClick(support)}
                        >
                            <TableCell padding="checkbox">
                                <Checkbox
                                    checked={selectedIds.includes(support.id)}
                                    onChange={() => onSelectOne(support.id)}
                                    onClick={e => e.stopPropagation()}
                                />
                            </TableCell>
                            {columnOrder.map(colKey => (
                                <TableCell key={colKey}>
                                    {colKey === 'costRate'
                                        ? support[colKey].toLocaleString('en-IN', { style: 'currency', currency: 'INR' })
                                        : support[colKey]}
                                </TableCell>
                            ))}
                            <TableCell align="center">
                                <ActionButtons support={support} onView={onView} onEdit={onEdit} onDelete={onDelete} />
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </AdminComponents.TableViewContainer>
    );
};

// Product Support Dialog Component
const ProductSupportDialog = ({ open, onClose, supportData, mode, onSave }) => {
  const [formData, setFormData] = useState({
    supportId: '',
    customerName: '',
    productName: '',
    category: 'Implementation Cost/Rate',
    description: '',
    costRate: '',
    currency: 'INR',
    duration: '',
    status: 'Active',
    startDate: '',
    endDate: '',
    assignedTo: '',
    priority: 'Medium',
    region: ''
  });

  // Initialize form data when dialog opens
  React.useEffect(() => {
    if (open) {
      if (supportData && (mode === 'edit' || mode === 'view')) {
        setFormData(supportData);
      } else {
        // Reset form for add mode
        setFormData({
          supportId: `PS-${new Date().getFullYear()}-${String(Date.now()).slice(-3)}`,
          customerName: '',
          productName: '',
          category: 'Implementation Cost/Rate',
          description: '',
          costRate: '',
          currency: 'INR',
          duration: '',
          status: 'Active',
          startDate: new Date().toISOString().split('T')[0],
          endDate: '',
          assignedTo: '',
          priority: 'Medium',
          region: ''
        });
      }
    }
  }, [open, supportData, mode]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    if (mode !== 'view') {
      // Basic validation
      if (!formData.supportId || !formData.customerName || !formData.productName || !formData.costRate) {
        alert('Please fill in all required fields');
        return;
      }
      onSave(formData);
    }
  };

  const isReadOnly = mode === 'view';
  const dialogTitle = mode === 'add' ? 'Add New Product Support' : mode === 'edit' ? 'Edit Product Support' : 'View Product Support';

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="md">
      <AdminComponents.DialogHeader>
        <Typography variant="h6">{dialogTitle}</Typography>
        <IconButton onClick={onClose} size="small">
          <Close />
        </IconButton>
      </AdminComponents.DialogHeader>
      <DialogContent>
        <Box sx={{ p: 2 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Support ID"
                value={formData.supportId}
                onChange={(e) => handleInputChange('supportId', e.target.value)}
                disabled={isReadOnly}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth disabled={isReadOnly} required>
                <InputLabel>Category</InputLabel>
                <Select
                  value={formData.category}
                  label="Category"
                  onChange={(e) => handleInputChange('category', e.target.value)}
                >
                  <MenuItem value="Implementation Cost/Rate">Implementation Cost/Rate</MenuItem>
                  <MenuItem value="Data Migration">Data Migration</MenuItem>
                  <MenuItem value="Interface Integration">Interface Integration</MenuItem>
                  <MenuItem value="Training Cost/Rate">Training Cost/Rate</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Customer Name"
                value={formData.customerName}
                onChange={(e) => handleInputChange('customerName', e.target.value)}
                disabled={isReadOnly}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Product Name"
                value={formData.productName}
                onChange={(e) => handleInputChange('productName', e.target.value)}
                disabled={isReadOnly}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                disabled={isReadOnly}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Cost/Rate (INR)"
                type="number"
                value={formData.costRate}
                onChange={(e) => handleInputChange('costRate', parseFloat(e.target.value) || 0)}
                disabled={isReadOnly}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Duration"
                value={formData.duration}
                onChange={(e) => handleInputChange('duration', e.target.value)}
                disabled={isReadOnly}
                placeholder="e.g., 3 months, 6 weeks"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Start Date"
                type="date"
                value={formData.startDate}
                onChange={(e) => handleInputChange('startDate', e.target.value)}
                disabled={isReadOnly}
                slotProps={{ inputLabel: { shrink: true } }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="End Date"
                type="date"
                value={formData.endDate}
                onChange={(e) => handleInputChange('endDate', e.target.value)}
                disabled={isReadOnly}
                slotProps={{ inputLabel: { shrink: true } }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Assigned To"
                value={formData.assignedTo}
                onChange={(e) => handleInputChange('assignedTo', e.target.value)}
                disabled={isReadOnly}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth disabled={isReadOnly}>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={formData.priority}
                  label="Priority"
                  onChange={(e) => handleInputChange('priority', e.target.value)}
                >
                  <MenuItem value="Low">Low</MenuItem>
                  <MenuItem value="Medium">Medium</MenuItem>
                  <MenuItem value="High">High</MenuItem>
                  <MenuItem value="Critical">Critical</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth disabled={isReadOnly}>
                <InputLabel>Status</InputLabel>
                <Select
                  value={formData.status}
                  label="Status"
                  onChange={(e) => handleInputChange('status', e.target.value)}
                >
                  <MenuItem value="Active">Active</MenuItem>
                  <MenuItem value="In Progress">In Progress</MenuItem>
                  <MenuItem value="Planning">Planning</MenuItem>
                  <MenuItem value="Scheduled">Scheduled</MenuItem>
                  <MenuItem value="Completed">Completed</MenuItem>
                  <MenuItem value="Cancelled">Cancelled</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Region"
                value={formData.region}
                onChange={(e) => handleInputChange('region', e.target.value)}
                disabled={isReadOnly}
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
      <DialogActions sx={{ p: 2, borderTop: '1px solid var(--border-color)' }}>
        <Button onClick={onClose} variant="outlined" startIcon={<Close />}>
          {mode === 'view' ? 'Close' : 'Cancel'}
        </Button>
        {mode !== 'view' && (
          <Button onClick={handleSave} variant="contained" startIcon={<Save />}>
            {mode === 'add' ? 'Add Product Support' : 'Save Changes'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export { ProductSupportCard, ProductSupportCompactCard, ProductSupportListItem, ProductSupportTable, ProductSupportDialog, initialProductSupports, ALL_COLUMNS };
