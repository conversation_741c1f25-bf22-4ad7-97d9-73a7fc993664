import React, { useState, useEffect } from 'react';
import Logo from './Logo';
// import './Header.css';
import AuthModal from '../../common/AuthModal';
import { MdShoppingCart } from 'react-icons/md';
import { useNavigate } from 'react-router-dom';
import Button from '@mui/material/Button';

const Header = ({ user, setUser, logout, cartCount, showAuthModal, setShowAuthModal, cartPage, isDefaultUserLoggedIn, setIsDefaultUserLoggedIn }) => {
    const navLinks = [
        { title: 'Industries', href: '#industries' },
        { title: 'Suites', tab: 'suites' },
        { title: 'Modules', tab: 'modules' },
        { title: 'Partners', href: '#partners' },
        { title: 'Pricing', href: '#pricing' },
        { title: 'FAQ', href: '#faq' },
        { title: 'About Us', href: '#about' },
        { title: 'Contact', href: '#contact' },
        { title: 'Admin', href: '/admin', isAdmin: true },
    ];

    // Track active tab for highlighting
    const [activeSolutionsTab, setActiveSolutionsTab] = useState('suites');

    // Listen for tab changes from Solutions section
    useEffect(() => {
        const handler = (e) => {
            if (e.detail && (e.detail.tab === 'suites' || e.detail.tab === 'modules')) {
                setActiveSolutionsTab(e.detail.tab);
            }
        };
        window.addEventListener('solutionsTabSwitch', handler);
        return () => window.removeEventListener('solutionsTabSwitch', handler);
    }, []);

    const handleSolutionsTab = (tab) => {
        setActiveSolutionsTab(tab); // highlight immediately
        const el = document.getElementById('solutions');
        if (el) {
            const y = el.getBoundingClientRect().top + window.pageYOffset - 60; // 60px offset for header
            window.scrollTo({ top: y, behavior: 'smooth' });
        }
        window.dispatchEvent(new CustomEvent('solutionsTabSwitch', { detail: { tab } }));
    };

    const [showProfileMenu, setShowProfileMenu] = useState(false);
    const [showSidebar, setShowSidebar] = useState(false);
    const handleProfileClick = () => setShowProfileMenu(v => !v);
    const handleLogout = () => {
      setShowProfileMenu(false);
      setShowSidebar(false);
      console.log('Logout called'); // Debug log
      logout();
    };
    // Placeholder for change password
    const handleChangePassword = () => {
      alert('Change password feature coming soon!');
      setShowProfileMenu(false);
    };

    const handleAdminAccess = () => {
        // Navigate directly to admin dashboard
        navigate('/admin');
    };

    const [isDesktop, setIsDesktop] = useState(window.innerWidth > 1200);
    const navigate = useNavigate();
    useEffect(() => {
      const handleResize = () => setIsDesktop(window.innerWidth > 1200);
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, []);

    if (cartPage) {
        return (
            <header className="site-header cart-header-custom">
                <div className="container">
                    <div className="header-inner">
                        <Logo />
                        <nav className="cart-header-nav">
                            <span className="cart-header-link" onClick={() => navigate('/')}>Home</span>
                            <span className="cart-header-link" onClick={() => navigate('/cart')}>Cart ({cartCount})</span>
                            <a href="#contact" className="cart-header-link">Contact</a>
                        </nav>
                        <button className="header-hamburger" onClick={() => setShowSidebar(true)} aria-label="Open menu">
                            <span />
                            <span />
                            <span />
                        </button>
                        {/* Only show on desktop */}
                        {isDesktop && (
                            <div className="header-cart-login-row">
                                {user ? (
                                    <div className="header-profile-wrapper">
                                        <div className="header-profile" onClick={handleProfileClick} tabIndex={0} role="button">
                                            <img src={user.photo} alt="Profile" className="header-profile-photo" />
                                            <span className="header-profile-name">{user.name}</span>
                                        </div>
                                        {showProfileMenu && (
                                            <div className="header-profile-menu">
                                                <div className="header-profile-menu-item" onClick={handleChangePassword}>Change Password</div>
                                                <div className="header-profile-menu-item" onClick={handleLogout}>Logout</div>
                                            </div>
                                        )}
                                    </div>
                                ) : (
                                    isDefaultUserLoggedIn ? (
                                        <div className="header-profile-wrapper">
                                            <div className="header-profile" onClick={handleProfileClick} tabIndex={0} role="button">
                                                <img src="src/assets/Images/social-icons/user.png" alt="Profile" className="header-profile-photo" />
                                                <span className="header-profile-name">User</span>
                                            </div>
                                            {showProfileMenu && (
                                                <div className="header-profile-menu">
                                                    <div className="header-profile-menu-item" onClick={handleChangePassword}>Change Password</div>
                                                    <div className="header-profile-menu-item" onClick={handleLogout}>Logout</div>
                                                </div>
                                            )}
                                        </div>
                                    ) : null
                                )}
                                {/* Request for Demo button - same as main page */}
                                <Button variant="contained" color="primary" href="#contact" sx={{ borderRadius: 50, fontWeight: 700 }}>
                                    Request For Demo
                                </Button>
                            </div>
                        )}
                    </div>
                </div>
                {/* Sidebar for mobile */}
                <div className={`header-sidebar${showSidebar ? ' open' : ''}`}>
                    <button className="header-sidebar-close" onClick={() => setShowSidebar(false)} aria-label="Close menu">&times;</button>
                    <ul className="header-sidebar-nav">
                        <li>
                            <span className="nav-link" onClick={() => { setShowSidebar(false); navigate('/'); }}>Home</span>
                        </li>
                        <li>
                            <span className="nav-link sidebar-cart-link" onClick={() => { setShowSidebar(false); navigate('/cart'); }}>
                                Cart{cartCount > 0 ? ` (${cartCount})` : ''}
                            </span>
                        </li>
                        <li>
                            {user ? (
                                <>
                                    <div className="header-profile-menu-item" onClick={handleChangePassword}>Change Password</div>
                                    <div className="header-profile-menu-item" onClick={handleLogout}>Logout</div>
                                </>
                            ) : (
                                isDefaultUserLoggedIn ? (
                                    <>
                                        <div className="header-profile-menu-item" onClick={handleChangePassword}>Change Password</div>
                                        <div className="header-profile-menu-item" onClick={handleLogout}>Logout</div>
                                    </>
                                ) : (
                                    <span
                                        className="auth-link-text auth-link-text-spaced"
                                        onClick={() => setShowAuthModal(true)}
                                        tabIndex={0}
                                        role="button"
                                    >
                                        Login/Sign Up
                                    </span>
                                )
                            )}
                        </li>
                        <li>
                            <Button variant="contained" color="primary" href="#contact" sx={{ borderRadius: 50, fontWeight: 700 }} onClick={() => setShowSidebar(false)}>
                                Request For Demo
                            </Button>
                        </li>
                    </ul>
                </div>
                {showSidebar && <div className="header-sidebar-backdrop" onClick={() => setShowSidebar(false)} />}
                {showAuthModal && (
                    <AuthModal
                        mode={"login"}
                        onClose={() => setShowAuthModal(false)}
                        onLogin={userObj => { setUser(userObj); setShowAuthModal(false); }}
                    />
                )}
            </header>
        );
    }

    return (
        <header className="site-header">
            <div className="container">
                <div className="header-inner">
                    <Logo />
                    <nav className="main-nav">
                        <ul>
                            {navLinks.map((link) => (
                                <li key={link.title} className={link.tab ? (activeSolutionsTab === link.tab ? 'active' : '') : ''}>
                                    <a
                                        href={link.tab ? '#solutions' : link.href}
                                        onClick={link.tab ? (e => { e.preventDefault(); handleSolutionsTab(link.tab); }) :
                                                link.isAdmin ? (e => { e.preventDefault(); handleAdminAccess(); }) : undefined}
                                    >
                                        {link.title}
                                    </a>
                                </li>
                            ))}
                        </ul>
                    </nav>
                    <button className="header-hamburger" onClick={() => setShowSidebar(true)} aria-label="Open menu">
                      <span />
                      <span />
                      <span />
                    </button>
                    {/* Only show on desktop */}
                    {isDesktop && (
                      <div className="header-cart-login-row">
                        
                        {user ? (
                          <div className="header-profile-wrapper">
                            <div className="header-profile" onClick={handleProfileClick} tabIndex={0} role="button">
                              <img src={user.photo} alt="Profile" className="header-profile-photo" />
                              <span className="header-profile-name">{user.name}</span>
                            </div>
                            {showProfileMenu && (
                              <div className="header-profile-menu">
                                <div className="header-profile-menu-item" onClick={handleChangePassword}>Change Password</div>
                                <div className="header-profile-menu-item" onClick={handleLogout}>Logout</div>
                              </div>
                            )}
                          </div>
                        ) : (
                          isDefaultUserLoggedIn ? (
                              <div className="header-profile-wrapper">
                                  <div className="header-profile" onClick={handleProfileClick} tabIndex={0} role="button">
                                      <img src="src/assets/Images/social-icons/user.png" alt="Profile" className="header-profile-photo" />
                                      <span className="header-profile-name">User</span>
                                  </div>
                                  {showProfileMenu && (
                                      <div className="header-profile-menu">
                                          <div className="header-profile-menu-item" onClick={handleChangePassword}>Change Password</div>
                                          <div className="header-profile-menu-item" onClick={handleLogout}>Logout</div>
                                      </div>
                                  )}
                              </div>
                          ) : null
                        )}
                        {/* Request for Demo button - same as main page */}
                        <Button variant="contained" color="primary" href="#contact" sx={{ borderRadius: 50, fontWeight: 700 }}>
                          Request For Demo
                        </Button>
                        <div className="header-cart-icon-wrap header-cart-icon-wrap-pointer" onClick={() => navigate('/cart')}>
                          <MdShoppingCart className="header-cart-icon" />
                          {cartCount > 0 && <span className="header-cart-badge">{cartCount}</span>}
                        </div>
                      </div>
                      
                    )}
                    {showAuthModal && (
                        <AuthModal
                            mode={"login"}
                            onClose={() => setShowAuthModal(false)}
                            onLogin={userObj => { setUser(userObj); setShowAuthModal(false); }}
                        />
                    )}
                    {/* Sidebar for mobile */}
                    <div className={`header-sidebar${showSidebar ? ' open' : ''}`}>
                      <button className="header-sidebar-close" onClick={() => setShowSidebar(false)} aria-label="Close menu">&times;</button>
                      <ul className="header-sidebar-nav">
                        {navLinks.map((link) => (
                          <li key={link.title} className={link.tab ? (activeSolutionsTab === link.tab ? 'active' : '') : ''}>
                            <a
                                href={link.tab ? '#solutions' : link.href}
                                onClick={link.tab ? (e => { e.preventDefault(); handleSolutionsTab(link.tab); setShowSidebar(false); }) :
                                        link.isAdmin ? (e => { e.preventDefault(); handleAdminAccess(); setShowSidebar(false); }) :
                                        (e => { setShowSidebar(false); })}
                            >
                                {link.title}
                            </a>
                          </li>
                        ))}
                        <li>
                          <span className="nav-link sidebar-cart-link" onClick={() => { setShowSidebar(false); navigate('/cart'); }}>
                            Cart{cartCount > 0 ? ` (${cartCount})` : ''}
                          </span>
                        </li>
                        <li>
                          {user ? (
                            <>
                              <div className="header-profile-menu-item" onClick={handleChangePassword}>Change Password</div>
                              <div className="header-profile-menu-item" onClick={handleLogout}>Logout</div>
                            </>
                          ) : (
                            isDefaultUserLoggedIn ? (
                                <>
                                    <div className="header-profile-menu-item" onClick={handleChangePassword}>Change Password</div>
                                    <div className="header-profile-menu-item" onClick={handleLogout}>Logout</div>
                                </>
                            ) : null
                          )}
                        </li>
                        <li>
                          <Button variant="contained" color="primary" href="#contact" sx={{ borderRadius: 50, fontWeight: 700 }} onClick={() => setShowSidebar(false)}>
                            Request For Demo
                          </Button>
                        </li>
                      </ul>
                    </div>
                    {showSidebar && <div className="header-sidebar-backdrop" onClick={() => setShowSidebar(false)} />}
                </div>
            </div>
        </header>
    );
};

export default Header;
