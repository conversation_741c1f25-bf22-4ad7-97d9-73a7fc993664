import React from 'react';
import { Routes, Route, Link } from 'react-router-dom';
import './App.css'; // We will put all styles here

// Import your page components
import SimpleLandingPage from './SimpleLandingPage';
import ComplexLandingPage from './ComplexLandingPage';
import ComplexDialog from './ComplexDialog';

// Component for the main navigation UI (Showcase)
const Showcase = () => (
  <div className="showcase-container">
    <h1 className="showcase-title">HCLSoftware AfterMarket Web Templates</h1>
    <p className="showcase-subtitle">Select a page to view</p>
    <div className="cards-container">
      <Link to="/simple-landing-page" className="card">
        {/* Placeholder for icon */}
        <div className="icon-placeholder simple-landing-icon"></div>
        <span className="card-title">Simple Landing Page</span>
      </Link>
      <Link to="/complex-landing-page" className="card">
        {/* Placeholder for icon */}
        <div className="icon-placeholder complex-landing-icon"></div>
        <span className="card-title">Complex Landing Page</span>
      </Link>
      <Link to="/complex-dialog" className="card">
        {/* Placeholder for icon */}
        <div className="icon-placeholder complex-dialog-icon"></div>
        <span className="card-title">Complex Dialog</span>
      </Link>
    </div>
  </div>
);


function App() {
  return (
    <Routes>
      {/* The root path "/" will now render the Showcase component */}
      <Route path="/" element={<Showcase />} />

      {/* Routes for the individual pages remain the same */}
      <Route path="/simple-landing-page" element={<SimpleLandingPage />} />
      <Route path="/complex-landing-page" element={<ComplexLandingPage />} />
      <Route path="/complex-dialog" element={<ComplexDialog />} />
    </Routes>
  );
}

export default App;