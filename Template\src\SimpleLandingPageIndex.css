@import url('https://fonts.googleapis.com/icon?family=Material+Icons');
/* HCLTech Roobert Font - Loaded from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@font-face {
    font-family: 'HCLTech Roobert';
    src: local('Inter'), url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    font-weight: normal;
    font-style: normal;
}

/* Custom, theme-specific scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--hcl-gray-200);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--hcl-gray-400);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--hcl-primary-dark);
}

/* CSS Custom Properties (Variables) */
:root {
    /* HCL Color Theme */
    --hcl-primary: #00B4D8;
    --hcl-primary-dark: #0077B6;
    --hcl-secondary: #90E0EF;
    --hcl-tertiary: #CAF0F8;
    --hcl-accent: #03045E;
    --hcl-dark: #023047;
    --hcl-light: #F8FDFF;
    --hcl-gray-100: #F1F5F9;
    --hcl-gray-200: #E2E8F0;
    --hcl-gray-300: #CBD5E1; /* Lighter muted icons */
    --hcl-gray-400: #94A3B8;
    --hcl-gray-500: #64748B;
    --hcl-gray-600: #475569;
    --hcl-gray-700: #334155;
    --hcl-gray-800: #1E293B;
    --hcl-gray-900: #0F172A;
    
    /* Wireframe Skeleton Colors */
    --wireframe-bg: #FFFFFF;
    --wireframe-lines-light: #EAEAEA;
    --wireframe-lines-dark: #D1D1D1;
    --wireframe-text: #BDBDBD;
    --wireframe-dark-accent: #424242;

    /* Layout Variables */
    --header-height: 48px;
    --sidebar-collapsed-width: 60px;
    --sidebar-expanded-width: 240px;
    --settings-drawer-width: 480px;
    --transition-speed: 0.2s;
    --transition-bezier: cubic-bezier(0.25, 0.8, 0.25, 1);
    --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --hover-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    --border-radius: 4px;
    --border-radius-sm: 2px;
    --border-radius-md: 6px;
    --border-radius-lg: 8px;
    --border-radius-full: 9999px;

    /* Spacing - Compact */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing: 12px;
    --spacing-md: 16px;
    --spacing-lg: 20px;
    --spacing-xl: 24px;
    --spacing-2xl: 48px;

    /* Typography */
    --font-family: 'HCLTech Roobert', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --text-xs: 11px;
    --text-sm: 12px;
    --text-base: 14px;
    --text-lg: 16px;
    --text-xl: 18px;
    --text-2xl: 20px;

    /* Font Weights */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;

    /* Theme Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --border-color: #e2e8f0;
    --primary-color: #000000;
    --primary-light: #1f2937;
    --accent-color: #06b6d4;
    --accent-bg: #ecfeff;
    --hover-color: #f1f5f9;
    --success-color: #10B981;
    --error-color: #EF4444;
    --warning-color: #F59E0B;
    --info-color: #3b82f6;
}

/* === Main layout and components from original CSS file === */
body.light-theme { background-color: var(--hcl-gray-100); color: var(--hcl-gray-900); font-family: var(--font-family); line-height: 1.4; font-size: var(--text-base); }
* { margin: 0; padding: 0; box-sizing: border-box; }
.app-container { height: 100vh; width: 100vw; overflow: hidden; position: relative; display: flex; }
.app-body { height: 100%; display: flex; flex-direction: column; flex-grow: 1; min-width: 0; }
.app-header { height: var(--header-height); background: var(--bg-primary); border-bottom: 1px solid var(--border-color); box-shadow: var(--card-shadow); z-index: 1100; display: flex; align-items: center; padding: 0 var(--spacing-md); flex-shrink: 0; position: relative; }
.main-content-area { flex-grow: 1; display: flex; min-height: 0; }
.wireframe-sidebar { height: 100%; flex-shrink: 0; width: var(--sidebar-collapsed-width); background-color: var(--wireframe-bg); border-right: 1px solid var(--wireframe-lines-light); transition: width 0.3s ease-in-out; z-index: 100; overflow-x: hidden; }
.wireframe-sidebar:hover { width: var(--sidebar-expanded-width); }
.content-scroll-wrapper { flex-grow: 1; overflow-y: auto; }
.settings-drawer { height: 100vh; width: var(--settings-drawer-width); position: fixed; top: 0; left: 0; background-color: var(--bg-secondary); overflow-y: auto; box-shadow: 2px 0 8px rgba(0,0,0,0.1); transform: translateX(-100%); transition: transform 0.4s var(--transition-bezier); z-index: 1400; }
.settings-drawer.open { transform: translateX(0); }
.app-body.settings-drawer-is-open { margin-left: var(--settings-drawer-width); }
.drawer-content-wrapper { width: 100%; padding: 1.5rem; }
.drawer-header { display: flex; justify-content: space-between; align-items: center; padding-bottom: 1rem; margin-bottom: 1.5rem; border-bottom: 1px solid var(--border-color); }
.drawer-header h3 { font-size: var(--text-xl); font-weight: var(--font-semibold); color: var(--text-primary); }
.drawer-header .close-btn { background: none; border: none; cursor: pointer; color: var(--text-muted); padding: 0.5rem; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; }
.drawer-header .close-btn:hover { background-color: var(--hover-color); color: var(--text-primary); }
.settings-panel { display: flex; flex-direction: column; gap: var(--spacing-lg); }
.settings-section { border-bottom: 1px solid var(--border-color); padding-bottom: var(--spacing-lg); }
.settings-section:last-child { border-bottom: none; }
.settings-section-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--spacing-md); }
.settings-section-title { font-size: var(--text-base); font-weight: var(--font-semibold); color: var(--text-primary); }
.settings-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: var(--spacing-md); }
.settings-control { display: flex; flex-direction: column; gap: var(--spacing-xs); }
.settings-control.full-width { grid-column: 1 / -1; }
.settings-control label { font-size: var(--text-sm); color: var(--text-secondary); font-weight: var(--font-medium); }
.settings-control input[type="text"], .settings-control input[type="number"], .settings-control select { width: 100%; padding: var(--spacing-sm); border: 1px solid var(--border-color); border-radius: var(--border-radius); font-size: var(--text-sm); background: var(--bg-primary); transition: var(--transition-speed); }
.settings-control input:focus, .settings-control select:focus { outline: none; border-color: var(--hcl-primary); box-shadow: 0 0 0 3px rgba(0, 180, 216, 0.1); }
.settings-control input[type="color"] { width: 100%; height: 38px; padding: var(--spacing-xs); border: 1px solid var(--border-color); border-radius: var(--border-radius); }
.settings-control.checkbox-control { flex-direction: row; align-items: center; }
.settings-control.checkbox-control label { margin-left: var(--spacing-sm); }
.drawer-actions { display: flex; justify-content: flex-end; gap: var(--spacing-sm); margin-top: var(--spacing-lg); }
.header-left { display: flex; align-items: center; gap: var(--spacing); }
.back-button { background: none; border: none; color: var(--text-primary); font-size: 24px; cursor: pointer; padding: 8px; border-radius: var(--border-radius); transition: var(--transition-speed); }
.back-button:hover { background-color: var(--hover-color); }
.page-title { font-size: var(--text-xl); font-weight: var(--font-semibold); color: var(--text-primary); opacity: 0.5; }
.header-right { margin-left: auto; display: flex; align-items: center; gap: var(--spacing-sm); }
.btn { padding: var(--spacing-xs) var(--spacing); border: none; border-radius: var(--border-radius); font-size: var(--text-sm); font-weight: var(--font-medium); cursor: pointer; transition: var(--transition-speed); display: flex; align-items: center; gap: var(--spacing-xs); text-decoration: none; height: 32px; }
.btn-primary { background: var(--hcl-primary); color: white; }
.btn-primary:hover { background: var(--hcl-primary-dark); transform: translateY(-1px); }
.btn-secondary { background: var(--bg-primary); color: var(--text-primary); border: 1px solid var(--border-color); }
.btn-secondary:hover { background: var(--hover-color); }
.main-content { max-width: 100%; margin-left: auto; margin-right: auto; padding: var(--spacing); }
.stats-grid { display: flex; align-items: center; gap: var(--spacing); margin-bottom: var(--spacing-md); padding: var(--spacing); background: var(--bg-primary); border: 1px solid var(--border-color); border-radius: var(--border-radius); box-shadow: var(--card-shadow); flex-wrap: wrap; justify-content: space-between;height: 110px; }
.Activity-grid { display: flex; align-items: center; gap: var(--spacing); margin-top: var(--spacing-md); padding: var(--spacing); background: var(--bg-primary); border: 1px solid var(--border-color); border-radius: var(--border-radius); box-shadow: var(--card-shadow); flex-wrap: wrap; justify-content: space-between;height: 110px; }
.controls-section { background: var(--bg-primary); border: 1px solid var(--border-color); border-radius: var(--border-radius); padding: var(--spacing); margin-bottom: var(--spacing-md); box-shadow: var(--card-shadow); }
.search-filters { display: flex; align-items: center; gap: var(--spacing-sm); flex-wrap: wrap; justify-content: space-between; }
.search-group { position: relative; flex: 1; min-width: 200px; max-width: 100%; }
.search-input { width: 100%; padding: var(--spacing-sm) var(--spacing) var(--spacing-sm) 32px; border: 1px solid var(--border-color); border-radius: var(--border-radius); font-size: var(--text-sm); background: var(--bg-primary); transition: var(--transition-speed); height: 38px; }
.search-icon { position: absolute; left: 10px; top: 50%; transform: translateY(-50%); color: var(--text-muted); font-size: 16px; }
.status-filter-group { display: flex; align-items: center; gap: var(--spacing); background: var(--bg-primary); border-radius: var(--border-radius); min-height: 38px; flex-wrap: wrap; flex-shrink: 0; }
.status-filter-group .filter-label { font-weight: var(--font-medium); color: var(--text-secondary); font-size: var(--text-sm); margin-right: var(--spacing-xs); white-space: nowrap; }
.radio-group { display: flex; gap: var(--spacing); align-items: center; }
.radio-option { display: flex; align-items: center; gap: var(--spacing-xs); cursor: pointer; }
.radio-option input[type="radio"] { display: none; }
.radio-custom { width: 16px; height: 16px; border: 2px solid var(--border-color); border-radius: 50%; position: relative; }
.radio-option input[type="radio"]:checked + .radio-custom { border-color: var(--hcl-primary); background: var(--hcl-primary); }
.radio-option input[type="radio"]:checked + .radio-custom::after { content: ''; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 6px; height: 6px; background: white; border-radius: 50%; }
.radio-text { font-size: var(--text-sm); color: var(--text-primary); font-weight: var(--font-medium); }
.view-toggle { display: flex; background: var(--bg-secondary); border-radius: var(--border-radius); padding: 4px; border: 1px solid var(--border-color); margin-left: auto; }
.view-btn { padding: 3px 5px; border: none; background: none; color: var(--text-secondary); cursor: pointer; border-radius: var(--border-radius-sm); transition: var(--transition-speed); display: flex; align-items: center; justify-content: center; }
.view-btn.active { background: var(--hcl-primary); color: white; }
.view-btn .material-icons { font-size: 18px; color: inherit; }

/* === START: NEW 70/30 SPLIT LAYOUT === */
.content-wrapper-split {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-start;
}
.content-pane-left {
    flex: 0 0 70%;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing);
    height: calc(100vh - 280px); /* Adjust this value if header/filter heights change */
    overflow-y: auto;
}
.blank-pane-right {
    flex: 1 1 30%;
    height: calc(100vh - 280px); /* Match the height of the left pane */
}
.blank-pane-right .technician-card {
    height: 100%;
    min-height: unset; /* Override default min-height */
}
/* === END: NEW 70/30 SPLIT LAYOUT === */

/* Base Card Styling */
.technician-card { background: var(--bg-primary); border: 1px solid var(--border-color); color: var(--text-primary); border-radius: var(--border-radius); transition: var(--transition-speed); position: relative; overflow: hidden; cursor: pointer; display: flex; flex-direction: column; min-height: 300px; }
.card-header-content, .card-footer-content { padding: var(--spacing-xs) var(--spacing-sm); background-color: rgba(0, 0, 0, 0.05); border-bottom: 1px solid rgba(0, 0, 0, 0.1); font-size: var(--text-sm); font-weight: var(--font-medium); color: inherit; }
.card-footer-content { border-top: 1px solid rgba(0, 0, 0, 0.1); border-bottom: none; margin-top: auto;}
.card-content-placeholder { flex-grow: 1; }
.card-header { display: flex; justify-content: flex-end; align-items: flex-start; min-height: 48px; padding: var(--spacing-sm); position: absolute; top: 0; right: 0; z-index: 10; }
.card-actions { display: flex; gap: 4px; }
.action-btn { width: 32px; height: 32px; border: none; border-radius: var(--border-radius); background: var(--bg-secondary); color: var(--hcl-gray-300); cursor: pointer; transition: var(--transition-speed); display: flex; align-items: center; justify-content: center; font-size: 16px; }
.action-btn:hover { color: var(--text-primary); }
.fab-container { 
    position: fixed; 
    bottom: var(--spacing-lg); 
    right: var(--spacing-lg); 
    z-index: 1100;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.main-fab { 
    width: 56px; 
    height: 56px; 
    border-radius: 50%; 
    display: flex; 
    align-items: center; 
    justify-content: center; 
    font-size: 24px; 
    cursor: pointer; 
    transition: transform 0.2s ease, box-shadow 0.2s ease; 
    color: white; 
    box-shadow: var(--card-shadow); 
    border:none; 
    margin-top: 16px; /* Add margin to separate from options */
    order: 2; /* Ensure main fab is at the bottom */
}
.fab-options {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    list-style: none;
    padding: 0;
    margin: 0;
    order: 1; /* Ensure options are at the top */
    transition: all 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
    /* Hidden by default */
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
}
.fab-options.open {
    /* Visible on hover */
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}
.fab-option-btn {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: var(--hcl-gray-600);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--card-shadow);
    transition: all 0.2s ease;
}
.fab-option-btn:hover {
    background: var(--hcl-gray-700);
    transform: scale(1.1);
}
.fab-option-btn .material-icons {
    font-size: 22px;
}

/* === Responsive Adjustments === */
@media (max-width: 992px) {
    .content-wrapper-split {
        flex-direction: column;
    }
    .content-pane-left, .blank-pane-right {
        flex: 1 1 100%;
        height: auto;
        min-height: 400px;
    }
     .blank-pane-right {
        display: none;
    }
}

/* === START: NEW EXPORT MODAL STYLES === */
.export-modal-overlay {
  position: fixed; top: 0; left: 0; width: 100vw; height: 100vh;
  background-color: rgba(15, 23, 42, 0.7); /* Use a dark overlay from the color palette */
  backdrop-filter: blur(4px);
  display: flex; align-items: center; justify-content: center;
  z-index: 3000;
  opacity: 0;
  animation: fadeIn 0.3s forwards;
}

.export-modal-content {
  background: var(--bg-primary);
  width: 80vw;
  max-width: 900px;
  height: 80vh;
  border-radius: var(--border-radius-lg);
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0,0,0,0.2);
  transform: scale(0.95);
  animation: zoomIn 0.3s forwards;
}

@keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
@keyframes zoomIn { from { transform: scale(0.95); } to { transform: scale(1); } }

.export-modal-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}
.export-modal-header h3 { margin: 0; font-size: var(--text-lg); font-weight: var(--font-semibold); color: var(--text-primary); }
.export-modal-header .close-btn {
    background: none; border: none; cursor: pointer; color: var(--text-muted);
    padding: 0.5rem; border-radius: 50%; display: inline-flex;
    align-items: center; justify-content: center;
}
.export-modal-header .close-btn:hover { background-color: var(--hover-color); color: var(--text-primary); }

.export-modal-body {
  flex-grow: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  padding: 0.5rem 1.5rem 1.5rem 1.5rem;
}

.tab-buttons {
  display: flex;
  gap: 0.5rem;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 1rem;
  flex-shrink: 0;
}
.tab-buttons button {
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  cursor: pointer;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-muted);
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}
.tab-buttons button.active {
  font-weight: var(--font-semibold);
  color: var(--hcl-primary);
  border-bottom-color: var(--hcl-primary);
}

.code-display-container {
  position: relative;
  flex-grow: 1;
  min-height: 0;
  background-color: var(--bg-tertiary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.code-display-container pre {
  margin: 0;
  padding: 1rem;
  height: 100%;
  overflow: auto;
  font-family: 'Courier New', Courier, monospace;
  font-size: 0.85rem;
  line-height: 1.5;
  color: var(--text-primary);
}

.code-display-container .copy-btn {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    transition: all 0.2s;
}
.code-display-container .copy-btn:hover {
    background: var(--hover-color);
    color: var(--text-primary);
    border-color: var(--hcl-gray-300);
}

.export-modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  flex-shrink: 0;
}

.btn-export {
    background: var(--hcl-gray-700);
    color: white;
    border: 1px solid var(--hcl-gray-700);
    margin-right: auto;
}
.btn-export:hover {
    background: var(--hcl-gray-900);
}
/* === END: NEW EXPORT MODAL STYLES === */
/* === START: NEW EXPORT MODAL STYLES (Add to all three CSS files) === */
.export-modal-overlay {
  position: fixed; top: 0; left: 0; width: 100vw; height: 100vh;
  background-color: rgba(15, 23, 42, 0.7); /* Use a dark overlay from the color palette */
  backdrop-filter: blur(4px);
  display: flex; align-items: center; justify-content: center;
  z-index: 3000;
  opacity: 0;
  animation: fadeIn 0.3s forwards;
}

.export-modal-content {
  background: var(--bg-primary);
  width: 80vw;
  max-width: 900px;
  height: 80vh;
  border-radius: var(--border-radius-lg);
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0,0,0,0.2);
  transform: scale(0.95);
  animation: zoomIn 0.3s forwards;
}

@keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
@keyframes zoomIn { from { transform: scale(0.95); } to { transform: scale(1); } }

.export-modal-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}
.export-modal-header h3 { margin: 0; font-size: var(--text-lg); font-weight: var(--font-semibold); color: var(--text-primary); }
.export-modal-header .close-btn {
    background: none; border: none; cursor: pointer; color: var(--text-muted);
    padding: 0.5rem; border-radius: 50%; display: inline-flex;
    align-items: center; justify-content: center;
}
.export-modal-header .close-btn:hover { background-color: var(--hover-color); color: var(--text-primary); }

.export-modal-body {
  flex-grow: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  padding: 0.5rem 1.5rem 1.5rem 1.5rem;
}

.tab-buttons {
  display: flex;
  gap: 0.5rem;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 1rem;
  flex-shrink: 0;
}
.tab-buttons button {
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  cursor: pointer;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-muted);
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}
.tab-buttons button.active {
  font-weight: var(--font-semibold);
  color: var(--hcl-primary);
  border-bottom-color: var(--hcl-primary);
}

.code-display-container {
  position: relative;
  flex-grow: 1;
  min-height: 0;
  background-color: var(--bg-tertiary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.code-display-container pre {
  margin: 0;
  padding: 1rem;
  height: 100%;
  overflow: auto;
  font-family: 'Courier New', Courier, monospace;
  font-size: 0.85rem;
  line-height: 1.5;
  color: var(--text-primary);
}

.code-display-container .copy-btn {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    transition: all 0.2s;
}
.code-display-container .copy-btn:hover {
    background: var(--hover-color);
    color: var(--text-primary);
    border-color: var(--hcl-gray-300);
}

.export-modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  flex-shrink: 0;
}

.btn-export {
    background: var(--hcl-gray-700);
    color: white;
    border: 1px solid var(--hcl-gray-700);
    margin-right: auto;
}
.btn-export:hover {
    background: var(--hcl-gray-900);
}
/* === END: NEW EXPORT MODAL STYLES === */