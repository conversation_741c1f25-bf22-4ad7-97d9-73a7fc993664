import React, { useState, useMemo } from 'react';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    ToggleButton, FormControlLabel, Drawer, Chip, FormControl, InputLabel, Select, MenuItem,
    Tabs, Tab, Grid, Dialog, DialogContent, DialogActions
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, People, CheckCircle, Cancel, WarningAmber, ShowChart, PieChart, DonutLarge,
    Settings, Save, Close, Upload, CloudUpload, AccountBalance, Assessment,
    Description, Download, Print, AttachMoney, Build, Storage, School, IntegrationInstructions
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';

// Mock product support data with the four categories
const initialProductSupports = [
  {
    id: 'ps-001',
    supportId: 'PS-2025-001',
    customerName: 'Acme Global Services',
    productName: 'HCL Commerce Suite',
    category: 'Implementation Cost/Rate',
    description: 'Complete implementation of HCL Commerce Suite with custom configurations',
    costRate: 150000,
    currency: 'INR',
    duration: '3 months',
    status: 'Active',
    startDate: '2025-01-15',
    endDate: '2025-04-15',
    assignedTo: 'Implementation Team A',
    priority: 'High',
    region: 'North America',
    createdBy: 'John Smith',
    createdDate: '2025-01-10T10:30:00Z'
  },
  {
    id: 'ps-002',
    supportId: 'PS-2025-002',
    customerName: 'TechGlobal Solutions',
    productName: 'HCL Digital Experience',
    category: 'Data Migration',
    description: 'Migration of legacy data to HCL Digital Experience platform',
    costRate: 85000,
    currency: 'INR',
    duration: '6 weeks',
    status: 'In Progress',
    startDate: '2025-02-01',
    endDate: '2025-03-15',
    assignedTo: 'Data Migration Team',
    priority: 'Medium',
    region: 'Europe',
    createdBy: 'Sarah Johnson',
    createdDate: '2025-01-25T14:45:00Z'
  },
  {
    id: 'ps-003',
    supportId: 'PS-2025-003',
    customerName: 'Global Manufacturing Ltd',
    productName: 'HCL Manufacturing Suite',
    category: 'Interface Integration',
    description: 'Integration with existing ERP and CRM systems',
    costRate: 120000,
    currency: 'INR',
    duration: '8 weeks',
    status: 'Planning',
    startDate: '2025-03-01',
    endDate: '2025-04-26',
    assignedTo: 'Integration Team B',
    priority: 'High',
    region: 'Asia',
    createdBy: 'Michael Chen',
    createdDate: '2025-02-15T09:00:00Z'
  },
  {
    id: 'ps-004',
    supportId: 'PS-2025-004',
    customerName: 'Energy Solutions Inc',
    productName: 'HCL Energy Suite',
    category: 'Training Cost/Rate',
    description: 'Comprehensive user training and certification program',
    costRate: 45000,
    currency: 'INR',
    duration: '4 weeks',
    status: 'Scheduled',
    startDate: '2025-04-01',
    endDate: '2025-04-30',
    assignedTo: 'Training Team',
    priority: 'Medium',
    region: 'Europe',
    createdBy: 'Lisa Anderson',
    createdDate: '2025-03-10T13:00:00Z'
  },
  {
    id: 'ps-005',
    supportId: 'PS-2025-005',
    customerName: 'Logistics Corp APAC',
    productName: 'HCL Logistics Suite',
    category: 'Implementation Cost/Rate',
    description: 'Full implementation with custom workflow automation',
    costRate: 200000,
    currency: 'INR',
    duration: '4 months',
    status: 'Active',
    startDate: '2025-02-15',
    endDate: '2025-06-15',
    assignedTo: 'Implementation Team C',
    priority: 'High',
    region: 'Asia',
    createdBy: 'David Kim',
    createdDate: '2025-02-10T15:30:00Z'
  }
];

const ALL_COLUMNS = [
  { key: 'supportId', label: 'Support ID', type: 'string', groupable: true },
  { key: 'customerName', label: 'Customer Name', type: 'string', groupable: true },
  { key: 'productName', label: 'Product Name', type: 'string', groupable: true },
  { key: 'category', label: 'Category', type: 'string', groupable: true },
  { key: 'costRate', label: 'Cost/Rate (INR)', type: 'number', groupable: false },
  { key: 'duration', label: 'Duration', type: 'string', groupable: true },
  { key: 'status', label: 'Status', type: 'string', groupable: true },
  { key: 'priority', label: 'Priority', type: 'string', groupable: true },
];

const FILTER_OPERATORS = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'];

const ActionButtons = ({ support, onView, onEdit, onDelete }) => (
    <Box onClick={e => e.stopPropagation()}>
        <IconButton size="small" onClick={() => onView(support)} title="View Details">
            <Visibility fontSize="small" />
        </IconButton>
        <IconButton size="small" onClick={() => onEdit(support)} title="Edit">
            <Edit fontSize="small" />
        </IconButton>
        <IconButton size="small" color="error" onClick={() => onDelete([support.id])} title="Delete">
            <Delete fontSize="small" />
        </IconButton>
    </Box>
);

const ProductSupportCard = ({ support, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
  <AdminComponents.CardBase isSelected={isSelected}>
    <Checkbox
      className="card-checkbox"
      checked={isChecked}
      onChange={() => onSelect(support.id)}
      onClick={e => e.stopPropagation()}
    />
    <AdminComponents.CardActionContainer>
      <ActionButtons support={support} onView={onView} onEdit={onEdit} onDelete={onDelete} />
    </AdminComponents.CardActionContainer>
    <AdminComponents.PaddedCardContent>
      <Typography variant="h6" component="div" noWrap>{support.supportId}</Typography>
      <Typography color="text.secondary" noWrap gutterBottom>{support.customerName}</Typography>
      <AdminComponents.StatusBadge
        ownerState={{ status: support.status }}
        label={support.status}
        size="small"
      />
      <AdminComponents.CardDivider />
      <AdminComponents.CardDetailsGrid>
        <AdminComponents.CardDetailLabel variant="body2">Category:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>{support.category}</Typography>
        <AdminComponents.CardDetailLabel variant="body2">Product:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>{support.productName}</Typography>
        <AdminComponents.CardDetailLabel variant="body2">Cost/Rate:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>
          {support.costRate.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
        </Typography>
        <AdminComponents.CardDetailLabel variant="body2">Duration:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>{support.duration}</Typography>
      </AdminComponents.CardDetailsGrid>
    </AdminComponents.PaddedCardContent>
  </AdminComponents.CardBase>
);

const ProductSupportCompactCard = ({ support, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
  <AdminComponents.CardBase isSelected={isSelected}>
    <Checkbox
      className="card-checkbox"
      checked={isChecked}
      onChange={() => onSelect(support.id)}
      onClick={e => e.stopPropagation()}
    />
    <AdminComponents.CardActionContainer>
      <ActionButtons support={support} onView={onView} onEdit={onEdit} onDelete={onDelete} />
    </AdminComponents.CardActionContainer>
    <AdminComponents.CompactCardContent>
      <div>
        <Typography variant="subtitle1" fontWeight="bold" noWrap>{support.supportId}</Typography>
        <Typography variant="caption" color="text.secondary">{support.customerName}</Typography>
      </div>
      <AdminComponents.CompactCardFooter>
        <Typography variant="body2" fontWeight="500">
          {support.costRate.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
        </Typography>
        <AdminComponents.StatusBadge
          ownerState={{ status: support.status }}
          label={support.status}
          size="small"
        />
      </AdminComponents.CompactCardFooter>
    </AdminComponents.CompactCardContent>
  </AdminComponents.CardBase>
);

const ProductSupportListItem = ({ support, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
  <AdminComponents.ListItemCard isSelected={isSelected}>
    <AdminComponents.ListItemGrid>
      <Checkbox
        checked={isChecked}
        onChange={() => onSelect(support.id)}
        onClick={e => e.stopPropagation()}
      />
      <Box>
        <Typography fontWeight="bold">{support.supportId}</Typography>
        <Typography variant="body2" color="text.secondary">{support.customerName}</Typography>
      </Box>
      <Typography variant="body2">{support.category}</Typography>
      <Typography variant="body2">{support.productName}</Typography>
      <Typography variant="body2">
        {support.costRate.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
      </Typography>
      <Typography variant="body2">{support.duration}</Typography>
      <AdminComponents.StatusBadge
        ownerState={{ status: support.status }}
        label={support.status}
        size="small"
      />
      <AdminComponents.ListItemActions>
        <ActionButtons support={support} onView={onView} onEdit={onEdit} onDelete={onDelete} />
      </AdminComponents.ListItemActions>
    </AdminComponents.ListItemGrid>
  </AdminComponents.ListItemCard>
);

const ProductSupportTable = ({
  supports,
  onRowClick,
  onHeaderClick,
  sortColumn,
  sortDirection,
  selectedId,
  selectedIds,
  onSelectAll,
  onSelectOne,
  columnOrder,
  setColumnOrder,
  onDelete,
  onEdit,
  onView
}) => {
    const dragItemIndex = React.useRef(null);
    const dragOverItemIndex = React.useRef(null);

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };

    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox">
                            <Checkbox
                                indeterminate={selectedIds.length > 0 && selectedIds.length < supports.length}
                                checked={supports.length > 0 && selectedIds.length === supports.length}
                                onChange={onSelectAll}
                            />
                        </TableCell>
                        {columnOrder.map((colKey, idx) => (
                            <TableCell
                                key={colKey}
                                draggable
                                onDragStart={() => { dragItemIndex.current = idx; }}
                                onDragEnter={() => { dragOverItemIndex.current = idx; }}
                                onDragEnd={handleDrop}
                                onDragOver={e => e.preventDefault()}
                                style={{ cursor: 'move' }}
                            >
                                <TableSortLabel
                                    active={sortColumn === colKey}
                                    direction={sortDirection}
                                    onClick={() => onHeaderClick(colKey)}
                                >
                                    {ALL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </TableCell>
                        ))}
                        <TableCell align="center">Actions</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {supports.map(support => (
                        <TableRow
                            key={support.id}
                            hover
                            selected={selectedId === support.id}
                            onClick={() => onRowClick(support)}
                        >
                            <TableCell padding="checkbox">
                                <Checkbox
                                    checked={selectedIds.includes(support.id)}
                                    onChange={() => onSelectOne(support.id)}
                                    onClick={e => e.stopPropagation()}
                                />
                            </TableCell>
                            {columnOrder.map(colKey => (
                                <TableCell key={colKey}>
                                    {colKey === 'costRate'
                                        ? support[colKey].toLocaleString('en-IN', { style: 'currency', currency: 'INR' })
                                        : support[colKey]}
                                </TableCell>
                            ))}
                            <TableCell align="center">
                                <ActionButtons support={support} onView={onView} onEdit={onEdit} onDelete={onDelete} />
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </AdminComponents.TableViewContainer>
    );
};

const kpiCards = [
  {
    key: 'open',
    title: 'OPEN TICKETS',
    value: 24,
    subtitle: 'Active support requests',
    icon: <FaTicketAlt style={{color:'#fbbf24',fontSize:32}} />,
    color: '#fbbf24',
  },
  {
    key: 'resolved',
    title: 'RESOLVED TODAY',
    value: 8,
    subtitle: <span><span style={{color:'#22c55e',fontWeight:600}}>+3</span> from yesterday</span>,
    icon: <FiCheckCircle style={{color:'#2dd4bf',fontSize:32}} />,
    color: '#2dd4bf',
  },
  {
    key: 'avgResponse',
    title: 'AVG. RESPONSE TIME',
    value: '2.5',
    subtitle: <span>hours</span>,
    icon: <FiClock style={{color:'#67e8f9',fontSize:32}} />,
    color: '#67e8f9',
  },
  {
    key: 'satisfaction',
    title: 'CUSTOMER SATISFACTION',
    value: '4.8',
    subtitle: <span>out of 5.0</span>,
    icon: <FiStar style={{color:'#2dd4bf',fontSize:32}} />,
    color: '#2dd4bf',
  },
];

const supportCategories = [
  {
    key: 'technical',
    title: 'Technical Issues',
    desc: 'Software bugs and technical problems',
    value: 12,
    icon: <FaCogs style={{color:'#2563eb',fontSize:32}} />,
    color: '#2563eb',
  },
  {
    key: 'install',
    title: 'Installation Support',
    desc: 'Help with software installation',
    value: 6,
    icon: <FiDownload style={{color:'#10b981',fontSize:32}} />,
    color: '#10b981',
  },
  {
    key: 'config',
    title: 'Configuration',
    desc: 'System setup and configuration',
    value: 4,
    icon: <FiSettings style={{color:'#f59e42',fontSize:32}} />,
    color: '#f59e42',
  },
  {
    key: 'training',
    title: 'Training & Documentation',
    desc: 'User training and documentation requests',
    value: 2,
    icon: <FiBookOpen style={{color:'#8b5cf6',fontSize:32}} />,
    color: '#8b5cf6',
  },
];

const WEEK_DAYS = [
  'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
];
const DEFAULT_DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
const DEFAULT_CHANNELS = ['Email Support', 'Live Chat', 'Phone Support', 'Microsoft Teams'];
const ALL_CHANNELS = [
  'Email Support',
  'Live Chat',
  'Phone Support',
  'Microsoft Teams',
  'Google Meet'
];
const TIMEZONES = [
  'India Standard Time (IST)',
  'Eastern Standard Time (EST)',
  'Central European Time (CET)',
  'Pacific Standard Time (PST)'
];

function formatTime(val) {
  // Format as HH:MM AM/PM
  if (!val) return '';
  const [h, m] = val.split(':');
  let hour = parseInt(h, 10);
  const ampm = hour >= 12 ? 'PM' : 'AM';
  hour = hour % 12 || 12;
  return `${hour.toString().padStart(2, '0')}:${m} ${ampm}`;
}

const SupportConfiguration = () => {
  const [days, setDays] = React.useState(DEFAULT_DAYS);
  const [startTime, setStartTime] = React.useState('09:00');
  const [endTime, setEndTime] = React.useState('17:00');
  const [timezone, setTimezone] = React.useState(TIMEZONES[0]);
  const [channels, setChannels] = React.useState(DEFAULT_CHANNELS);
  const handleDayToggle = (day) => {
    setDays(prev => prev.includes(day) ? prev.filter(d => d !== day) : [...prev, day]);
  };
  const handleChannelToggle = (ch) => {
    setChannels(prev => prev.includes(ch) ? prev.filter(c => c !== ch) : [...prev, ch]);
  };
  const handleSave = (e) => {
    e.preventDefault();
    // You can add save logic here
    alert('Configuration saved!');
  };
  return (
    <div className="support-config-section">
      <h2 className="support-config-title">Support Configuration</h2>
      <form onSubmit={handleSave} className="support-config-form">
        <div className="support-config-cards">
          {/* Days Support Card */}
          <div className="support-config-card">
            <div className="support-config-card-header">
              <FiCalendar style={{fontSize:22,color:'#0ea5e9'}} />
              <span className="support-config-card-title">Days Support</span>
            </div>
            <div className="support-config-card-desc">Select Support Days:</div>
            <div className="support-config-days-list">
              {WEEK_DAYS.map(day => (
                <label key={day} className="support-config-day-item">
                  <input type="checkbox" checked={days.includes(day)} onChange={() => handleDayToggle(day)} /> {day}
                </label>
              ))}
            </div>
          </div>
          {/* Support Hours Card */}
          <div className="support-config-card">
            <div className="support-config-card-header">
              <FiClock style={{fontSize:22,color:'#f59e42'}} />
              <span className="support-config-card-title">Support Hours</span>
            </div>
            <div className="support-config-hours-list">
              <label className="support-config-hours-label">
                Start Time:
                <input type="time" value={startTime} onChange={e => setStartTime(e.target.value)} className="support-config-hours-input" />
                <span className="support-config-hours-time">{formatTime(startTime)}</span>
              </label>
              <label className="support-config-hours-label">
                End Time:
                <input type="time" value={endTime} onChange={e => setEndTime(e.target.value)} className="support-config-hours-input" />
                <span className="support-config-hours-time">{formatTime(endTime)}</span>
              </label>
              <label className="support-config-hours-label">
                Timezone:
                <select value={timezone} onChange={e => setTimezone(e.target.value)} className="support-config-hours-select">
                  {TIMEZONES.map(tz => <option key={tz} value={tz}>{tz}</option>)}
                </select>
              </label>
            </div>
          </div>
          {/* Support Mode Card */}
          <div className="support-config-card">
            <div className="support-config-card-header">
              <FiHeadphones style={{fontSize:22,color:'#6366f1'}} />
              <span className="support-config-card-title">Support Mode</span>
            </div>
            <div className="support-config-card-desc">Available Support Channels:</div>
            <div className="support-config-mode-list">
              {ALL_CHANNELS.map(ch => (
                <label key={ch} className="support-config-mode-item">
                  <input type="checkbox" checked={channels.includes(ch)} onChange={() => handleChannelToggle(ch)} /> {ch}
                </label>
              ))}
            </div>
          </div>
        </div>
        <div className="support-config-save-btn-wrapper">
          <button type="submit" className="support-config-save-btn">
            <FiSave style={{fontSize:20}} /> Save Configuration
          </button>
        </div>
      </form>
    </div>
  );
};

const Support = () => {
    const [supports, setSupports] = useState(initialProductSupports);
    const [filteredSupports, setFilteredSupports] = useState(initialProductSupports);
    const [selectedId, setSelectedId] = useState(null);
    const [selectedIds, setSelectedIds] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [sortColumn, setSortColumn] = useState('createdDate');
    const [sortDirection, setSortDirection] = useState('desc');
    const [viewMode, setViewMode] = useState('table');
    const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(col => col.key));
    const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);

    // Dialog states
    const [dialogOpen, setDialogOpen] = useState(false);
    const [dialogMode, setDialogMode] = useState('add'); // 'add', 'edit', 'view'
    const [dialogSupport, setDialogSupport] = useState(null);

    // Success message state
    const [successMessage, setSuccessMessage] = useState('');

    // Filter and search logic
    const handleSearch = (term) => {
        setSearchTerm(term);
        if (!term.trim()) {
            setFilteredSupports(supports);
            return;
        }

        const filtered = supports.filter(support =>
            Object.values(support).some(value =>
                value && value.toString().toLowerCase().includes(term.toLowerCase())
            )
        );
        setFilteredSupports(filtered);
    };

    // Sorting logic
    const handleSort = (column) => {
        const isAsc = sortColumn === column && sortDirection === 'asc';
        const direction = isAsc ? 'desc' : 'asc';
        setSortColumn(column);
        setSortDirection(direction);

        const sorted = [...filteredSupports].sort((a, b) => {
            if (column === 'costRate') {
                return direction === 'asc' ? a[column] - b[column] : b[column] - a[column];
            }
            if (column === 'createdDate') {
                return direction === 'asc'
                    ? new Date(a[column]) - new Date(b[column])
                    : new Date(b[column]) - new Date(a[column]);
            }
            const aVal = a[column]?.toString().toLowerCase() || '';
            const bVal = b[column]?.toString().toLowerCase() || '';
            return direction === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
        });

        setFilteredSupports(sorted);
    };

    // Selection handlers
    const handleSelectAll = (event) => {
        if (event.target.checked) {
            setSelectedIds(filteredSupports.map(support => support.id));
        } else {
            setSelectedIds([]);
        }
    };

    const handleSelectOne = (id) => {
        setSelectedIds(prev =>
            prev.includes(id) ? prev.filter(selectedId => selectedId !== id) : [...prev, id]
        );
    };

    const handleRowClick = (support) => {
        setSelectedId(support.id);
    };

    // Dialog handlers
    const handleOpenDialog = (mode, support = null) => {
        setDialogMode(mode);
        setDialogSupport(support);
        setDialogOpen(true);
    };

    const handleCloseDialog = () => {
        setDialogOpen(false);
        setDialogMode('add');
        setDialogSupport(null);
    };

    const handleSaveDialog = (supportData) => {
        if (dialogMode === 'add') {
            const newSupport = {
                ...supportData,
                id: `ps-${Date.now()}`,
                createdDate: new Date().toISOString(),
                createdBy: 'Current User'
            };
            setSupports(prev => [...prev, newSupport]);
            setFilteredSupports(prev => [...prev, newSupport]);
            setSuccessMessage('Product Support added successfully!');
        } else if (dialogMode === 'edit') {
            setSupports(prev => prev.map(s => s.id === supportData.id ? supportData : s));
            setFilteredSupports(prev => prev.map(s => s.id === supportData.id ? supportData : s));
            setSuccessMessage('Product Support updated successfully!');
        }
        handleCloseDialog();

        // Clear success message after 3 seconds
        setTimeout(() => setSuccessMessage(''), 3000);
    };

    // Action handlers
    const handleView = (support) => {
        handleOpenDialog('view', support);
    };

    const handleEdit = (support) => {
        handleOpenDialog('edit', support);
    };

    const handleDelete = (ids) => {
        if (window.confirm(`Are you sure you want to delete ${ids.length} product support(s)?`)) {
            setSupports(prev => prev.filter(support => !ids.includes(support.id)));
            setFilteredSupports(prev => prev.filter(support => !ids.includes(support.id)));
            setSelectedIds([]);
            setSelectedId(null);
        }
    };

    const handleAdd = () => {
        handleOpenDialog('add');
    };

    // Summary calculations
    const summaryData = useMemo(() => {
        const totalSupports = filteredSupports.length;
        const totalCost = filteredSupports.reduce((sum, support) => sum + support.costRate, 0);
        const activeSupports = filteredSupports.filter(s => s.status === 'Active').length;
        const implementationSupports = filteredSupports.filter(s => s.category === 'Implementation Cost/Rate').length;
        const dataMigrationSupports = filteredSupports.filter(s => s.category === 'Data Migration').length;
        const integrationSupports = filteredSupports.filter(s => s.category === 'Interface Integration').length;
        const trainingSupports = filteredSupports.filter(s => s.category === 'Training Cost/Rate').length;

        return {
            totalSupports,
            totalCost,
            activeSupports,
            implementationSupports,
            dataMigrationSupports,
            integrationSupports,
            trainingSupports,
            averageCost: totalSupports > 0 ? totalCost / totalSupports : 0
        };
    }, [filteredSupports]);

    const renderContent = () => (
        <AdminComponents.ViewContainer>
            {filteredSupports.length > 0 ? (
                <>
                    {viewMode === 'grid' && (
                        <AdminComponents.GridView>
                            {filteredSupports.map(support => (
                                <ProductSupportCard
                                    key={support.id}
                                    support={support}
                                    isSelected={selectedId === support.id}
                                    onSelect={handleRowClick}
                                    isChecked={selectedIds.includes(support.id)}
                                    onDelete={handleDelete}
                                    onEdit={handleEdit}
                                    onView={handleView}
                                />
                            ))}
                        </AdminComponents.GridView>
                    )}
                    {viewMode === 'compact' && (
                        <AdminComponents.CompactView>
                            {filteredSupports.map(support => (
                                <ProductSupportCompactCard
                                    key={support.id}
                                    support={support}
                                    isSelected={selectedId === support.id}
                                    onSelect={handleRowClick}
                                    isChecked={selectedIds.includes(support.id)}
                                    onDelete={handleDelete}
                                    onEdit={handleEdit}
                                    onView={handleView}
                                />
                            ))}
                        </AdminComponents.CompactView>
                    )}
                    {viewMode === 'list' && (
                        <AdminComponents.ListView>
                            {filteredSupports.map(support => (
                                <ProductSupportListItem
                                    key={support.id}
                                    support={support}
                                    isSelected={selectedId === support.id}
                                    onSelect={handleRowClick}
                                    isChecked={selectedIds.includes(support.id)}
                                    onDelete={handleDelete}
                                    onEdit={handleEdit}
                                    onView={handleView}
                                />
                            ))}
                        </AdminComponents.ListView>
                    )}
                    {viewMode === 'table' && (
                        <ProductSupportTable
                            supports={filteredSupports}
                            onRowClick={handleRowClick}
                            onHeaderClick={handleSort}
                            sortColumn={sortColumn}
                            sortDirection={sortDirection}
                            selectedId={selectedId}
                            selectedIds={selectedIds}
                            onSelectAll={handleSelectAll}
                            onSelectOne={handleSelectOne}
                            columnOrder={columnOrder}
                            setColumnOrder={setColumnOrder}
                            onDelete={handleDelete}
                            onEdit={handleEdit}
                            onView={handleView}
                        />
                    )}
                </>
            ) : (
                <AdminComponents.CenteredMessage component={Paper}>
                    <AdminComponents.LargeIcon color="disabled" />
                    <Typography variant="h6">No Product Support Found</Typography>
                    <Typography color="text.secondary">
                        {searchTerm ? 'No product support matches your search criteria.' : 'No product support available.'}
                    </Typography>
                    <Button variant="contained" startIcon={<Add />} onClick={handleAdd}>
                        Add New Product Support
                    </Button>
                </AdminComponents.CenteredMessage>
            )}
        </AdminComponents.ViewContainer>
    );

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={filterDrawerOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={filterDrawerOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                {successMessage && (
                                    <Box sx={{ mb: 2 }}>
                                        <Typography
                                            variant="body2"
                                            sx={{
                                                color: 'var(--success-main)',
                                                backgroundColor: 'var(--success-light)',
                                                padding: '8px 16px',
                                                borderRadius: 'var(--radius-md)',
                                                border: '1px solid var(--success-main)'
                                            }}
                                        >
                                            {successMessage}
                                        </Typography>
                                    </Box>
                                )}
                                <AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="total">
                                            <Build />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{summaryData.implementationSupports}</Typography>
                                            <Typography variant="body2">Implementation Cost/Rate</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="active">
                                            <Storage />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{summaryData.dataMigrationSupports}</Typography>
                                            <Typography variant="body2">Data Migration</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="active">
                                            <IntegrationInstructions />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{summaryData.integrationSupports}</Typography>
                                            <Typography variant="body2">Interface Integration</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="inactive">
                                            <School />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{summaryData.trainingSupports}</Typography>
                                            <Typography variant="body2">Training Cost/Rate</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                                    <Button
                                        variant="contained"
                                        startIcon={<Add />}
                                        onClick={handleAdd}
                                    >
                                        Add Product Support
                                    </Button>
                                    {selectedIds.length > 0 && (
                                        <Button
                                            variant="outlined"
                                            color="error"
                                            startIcon={<Delete />}
                                            onClick={() => handleDelete(selectedIds)}
                                        >
                                            Delete Selected ({selectedIds.length})
                                        </Button>
                                    )}
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>
        {view === 'list' ? (
          <div className="table-container">
            <table className="data-table">
              <thead>
                <tr>
                  <th># Ticket ID</th>
                  <th><span role="img" aria-label="customer">🏢</span> Customer</th>
                  <th><span role="img" aria-label="category">��️</span> Category</th>
                  <th><span role="img" aria-label="priority">ℹ️</span> Priority</th>
                  <th><span role="img" aria-label="status">ℹ️</span> Status</th>
                  <th><span role="img" aria-label="created">📅</span> Created</th>
                  <th><span role="img" aria-label="assigned">👤</span> Assigned To</th>
                  <th><span role="img" aria-label="actions">⚙️</span> Actions</th>
                </tr>
              </thead>
              <tbody>
                {paged.length > 0 ? paged.map((c, idx) => (
                  <tr key={c.id}>
                    <td>{c.ticketId || `TKT-${c.id || idx+1}`}</td>
                    <td>{c.customer || 'Demo Customer'}</td>
                    <td>{c.category || 'General'}</td>
                    <td>{c.priority || 'Medium'}</td>
                    <td>{c.status || 'Open'}</td>
                    <td>{c.created || '2024-01-01'}</td>
                    <td>{c.assignedTo || 'Support Agent'}</td>
                    <td className="action-buttons">
                      <button onClick={() => openModal(c)} aria-label="Edit"><FiEdit /></button>
                      <button onClick={() => handleDelete(c.id)} aria-label="Delete"><FiTrash2 /></button>
                    </td>
                  </tr>
                )) : (
                  <tr>
                    <td colSpan="8" className="support-table-empty">No support details found.</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="support-grid-view">
            {paged.map((c) => (
              <Card
                key={c.id}
                title={c.product}
                subtitle={`Level: ${c.supportLevel}`}
                status={null}
                icon={null}
                actions={
                  <>
                    <button onClick={() => openModal(c)} aria-label="Edit"><FiEdit /></button>
                    <button onClick={() => handleDelete(c.id)} aria-label="Delete"><FiTrash2 /></button>
                  </>
                }
              >
                <div className="support-card-contact"><b>Contact:</b> {c.contact}</div>
              </Card>
            ))}
          </div>
        )}
        <div className="pagination">
            <button onClick={() => setPage(p => Math.max(1, p - 1))} disabled={page === 1}>Prev</button>
            <span>Page {page} of {totalPages}</span>
            <button onClick={() => setPage(p => Math.min(totalPages, p + 1))} disabled={page === totalPages}>Next</button>
        </div>
      </div>

      {showModal && (
        <Modal
          fields={FIELDS}
          data={selected}
          onClose={closeModal}
          onSave={selected ? handleEdit : handleAdd}
        />
      )}
      {toast && <Toast type={toast.type} message={toast.message} onClose={closeToast} />}
    </>
  );
};

export default Support; 