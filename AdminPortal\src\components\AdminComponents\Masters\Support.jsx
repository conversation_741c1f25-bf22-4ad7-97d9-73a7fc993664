import React, { useState, useMemo } from 'react';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    ToggleButton, FormControlLabel, Drawer, Chip, FormControl, InputLabel, Select, MenuItem,
    Tabs, Tab, Grid, Dialog, DialogContent, DialogActions
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, People, CheckCircle, Cancel, WarningAmber, ShowChart, PieChart, DonutLarge,
    Settings, Save, Close, Upload, CloudUpload, AccountBalance, Assessment,
    Description, Download, Print, AttachMoney, Build, Storage, School, IntegrationInstructions,
    SupportAgent, AccessTime, CalendarToday
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';
import { 
    ProductSupportCard, 
    ProductSupportCompactCard, 
    ProductSupportListItem, 
    ProductSupportTable, 
    ProductSupportDialog, 
    initialProductSupports, 
    ALL_COLUMNS 
} from './ProductSupport';

const Support = () => {
    const [supports, setSupports] = useState(initialProductSupports);
    const [filteredSupports, setFilteredSupports] = useState(initialProductSupports);
    const [selectedId, setSelectedId] = useState(null);
    const [selectedIds, setSelectedIds] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [sortColumn, setSortColumn] = useState('createdDate');
    const [sortDirection, setSortDirection] = useState('desc');
    const [viewMode, setViewMode] = useState('table');
    const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(col => col.key));
    const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
    
    // Dialog states
    const [dialogOpen, setDialogOpen] = useState(false);
    const [dialogMode, setDialogMode] = useState('add'); // 'add', 'edit', 'view'
    const [dialogSupport, setDialogSupport] = useState(null);
    
    // Success message state
    const [successMessage, setSuccessMessage] = useState('');

    // Mode of Support configuration states
    const [supportModeDialogOpen, setSupportModeDialogOpen] = useState(false);
    const [supportDays, setSupportDays] = useState(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']);
    const [supportModes, setSupportModes] = useState(['Chat', 'E-Mail', 'Phone']);
    const [supportStartTime, setSupportStartTime] = useState('09:00');
    const [supportEndTime, setSupportEndTime] = useState('17:00');

    // Add state for sidebar, filters, and group by
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [sidebarMode, setSidebarMode] = useState('search');
    const [stagedFilters, setStagedFilters] = useState([]);
    const [activeFilters, setActiveFilters] = useState([]);
    const [filterBuilder, setFilterBuilder] = useState({ field: '', operator: '', value: '' });
    const [groupByKeys, setGroupByKeys] = useState([]);

    const handleGroupByChange = (key) => {
      setGroupByKeys(prev =>
        prev.includes(key) ? prev.filter(k => k !== key) : [...prev, key]
      );
    };

    // Filter and search logic
    const handleSearch = (term) => {
        setSearchTerm(term);
        if (!term.trim()) {
            setFilteredSupports(supports);
            return;
        }

        const filtered = supports.filter(support =>
            Object.values(support).some(value =>
                value && value.toString().toLowerCase().includes(term.toLowerCase())
            )
        );
        setFilteredSupports(filtered);
    };

    // Sorting logic
    const handleSort = (column) => {
        const isAsc = sortColumn === column && sortDirection === 'asc';
        const direction = isAsc ? 'desc' : 'asc';
        setSortColumn(column);
        setSortDirection(direction);

        const sorted = [...filteredSupports].sort((a, b) => {
            if (column === 'costRate') {
                return direction === 'asc' ? a[column] - b[column] : b[column] - a[column];
            }
            if (column === 'createdDate') {
                return direction === 'asc' 
                    ? new Date(a[column]) - new Date(b[column])
                    : new Date(b[column]) - new Date(a[column]);
            }
            const aVal = a[column]?.toString().toLowerCase() || '';
            const bVal = b[column]?.toString().toLowerCase() || '';
            return direction === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
        });

        setFilteredSupports(sorted);
    };

    // Selection handlers
    const handleSelectAll = (event) => {
        if (event.target.checked) {
            setSelectedIds(filteredSupports.map(support => support.id));
        } else {
            setSelectedIds([]);
        }
    };

    const handleSelectOne = (id) => {
        setSelectedIds(prev =>
            prev.includes(id) ? prev.filter(selectedId => selectedId !== id) : [...prev, id]
        );
    };

    const handleRowClick = (support) => {
        setSelectedId(support.id);
    };

    // Dialog handlers
    const handleOpenDialog = (mode, support = null) => {
        setDialogMode(mode);
        setDialogSupport(support);
        setDialogOpen(true);
    };

    const handleCloseDialog = () => {
        setDialogOpen(false);
        setDialogMode('add');
        setDialogSupport(null);
    };

    const handleSaveDialog = (supportData) => {
        if (dialogMode === 'add') {
            const newSupport = {
                ...supportData,
                id: `ps-${Date.now()}`,
                createdDate: new Date().toISOString(),
                createdBy: 'Current User'
            };
            setSupports(prev => [...prev, newSupport]);
            setFilteredSupports(prev => [...prev, newSupport]);
            setSuccessMessage('Product Support added successfully!');
        } else if (dialogMode === 'edit') {
            setSupports(prev => prev.map(s => s.id === supportData.id ? supportData : s));
            setFilteredSupports(prev => prev.map(s => s.id === supportData.id ? supportData : s));
            setSuccessMessage('Product Support updated successfully!');
        }
        handleCloseDialog();
        
        // Clear success message after 3 seconds
        setTimeout(() => setSuccessMessage(''), 3000);
    };

    // Action handlers
    const handleView = (support) => {
        handleOpenDialog('view', support);
    };

    const handleEdit = (support) => {
        handleOpenDialog('edit', support);
    };

    const handleDelete = (ids) => {
        if (window.confirm(`Are you sure you want to delete ${ids.length} product support(s)?`)) {
            setSupports(prev => prev.filter(support => !ids.includes(support.id)));
            setFilteredSupports(prev => prev.filter(support => !ids.includes(support.id)));
            setSelectedIds([]);
            setSelectedId(null);
        }
    };

    const handleAdd = () => {
        handleOpenDialog('add');
    };

    // Mode of Support handlers
    const handleOpenSupportModeDialog = () => {
        setSupportModeDialogOpen(true);
    };

    const handleCloseSupportModeDialog = () => {
        setSupportModeDialogOpen(false);
    };

    const handleSaveSupportMode = () => {
        setSuccessMessage('Support mode configuration saved successfully!');
        setSupportModeDialogOpen(false);
        // Clear success message after 3 seconds
        setTimeout(() => setSuccessMessage(''), 3000);
    };

    const handleDayToggle = (day) => {
        setSupportDays(prev =>
            prev.includes(day)
                ? prev.filter(d => d !== day)
                : [...prev, day]
        );
    };

    const handleModeToggle = (mode) => {
        setSupportModes(prev =>
            prev.includes(mode)
                ? prev.filter(m => m !== mode)
                : [...prev, mode]
        );
    };

    const formatTime = (time) => {
        if (!time) return '';
        const [hours, minutes] = time.split(':');
        const hour = parseInt(hours, 10);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour % 12 || 12;
        return `${displayHour}:${minutes} ${ampm}`;
    };

    // Summary calculations
    const summaryData = useMemo(() => {
        const totalSupports = filteredSupports.length;
        const totalCost = filteredSupports.reduce((sum, support) => sum + support.costRate, 0);
        const activeSupports = filteredSupports.filter(s => s.status === 'Active').length;
        const implementationSupports = filteredSupports.filter(s => s.category === 'Implementation Cost/Rate').length;
        const dataMigrationSupports = filteredSupports.filter(s => s.category === 'Data Migration').length;
        const integrationSupports = filteredSupports.filter(s => s.category === 'Interface Integration').length;
        const trainingSupports = filteredSupports.filter(s => s.category === 'Training Cost/Rate').length;

        return {
            totalSupports,
            totalCost,
            activeSupports,
            implementationSupports,
            dataMigrationSupports,
            integrationSupports,
            trainingSupports,
            averageCost: totalSupports > 0 ? totalCost / totalSupports : 0
        };
    }, [filteredSupports]);

    const renderContent = () => (
        <AdminComponents.ViewContainer>
            {filteredSupports.length > 0 ? (
                <>
                    {viewMode === 'grid' && (
                        <AdminComponents.GridView>
                            {filteredSupports.map(support => (
                                <ProductSupportCard
                                    key={support.id}
                                    support={support}
                                    isSelected={selectedId === support.id}
                                    onSelect={handleRowClick}
                                    isChecked={selectedIds.includes(support.id)}
                                    onDelete={handleDelete}
                                    onEdit={handleEdit}
                                    onView={handleView}
                                />
                            ))}
                        </AdminComponents.GridView>
                    )}
                    {viewMode === 'compact' && (
                        <AdminComponents.CompactView>
                            {filteredSupports.map(support => (
                                <ProductSupportCompactCard
                                    key={support.id}
                                    support={support}
                                    isSelected={selectedId === support.id}
                                    onSelect={handleRowClick}
                                    isChecked={selectedIds.includes(support.id)}
                                    onDelete={handleDelete}
                                    onEdit={handleEdit}
                                    onView={handleView}
                                />
                            ))}
                        </AdminComponents.CompactView>
                    )}
                    {viewMode === 'list' && (
                        <AdminComponents.ListView>
                            {filteredSupports.map(support => (
                                <ProductSupportListItem
                                    key={support.id}
                                    support={support}
                                    isSelected={selectedId === support.id}
                                    onSelect={handleRowClick}
                                    isChecked={selectedIds.includes(support.id)}
                                    onDelete={handleDelete}
                                    onEdit={handleEdit}
                                    onView={handleView}
                                />
                            ))}
                        </AdminComponents.ListView>
                    )}
                    {viewMode === 'table' && (
                        <ProductSupportTable
                            supports={filteredSupports}
                            onRowClick={handleRowClick}
                            onHeaderClick={handleSort}
                            sortColumn={sortColumn}
                            sortDirection={sortDirection}
                            selectedId={selectedId}
                            selectedIds={selectedIds}
                            onSelectAll={handleSelectAll}
                            onSelectOne={handleSelectOne}
                            columnOrder={columnOrder}
                            setColumnOrder={setColumnOrder}
                            onDelete={handleDelete}
                            onEdit={handleEdit}
                            onView={handleView}
                        />
                    )}
                </>
            ) : (
                <AdminComponents.CenteredMessage component={Paper}>
                    <AdminComponents.LargeIcon color="disabled" />
                    <Typography variant="h6">No Product Support Found</Typography>
                    <Typography color="text.secondary">
                        {searchTerm ? 'No product support matches your search criteria.' : 'No product support available.'}
                    </Typography>
                    <Button variant="contained" startIcon={<Add />} onClick={handleAdd}>
                        Add New Product Support
                    </Button>
                </AdminComponents.CenteredMessage>
            )}
        </AdminComponents.ViewContainer>
    );

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={isSidebarOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={isSidebarOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                {successMessage && (
                                    <Box sx={{ mb: 2 }}>
                                        <Typography 
                                            variant="body2" 
                                            sx={{ 
                                                color: 'var(--success-main)', 
                                                backgroundColor: 'var(--success-light)', 
                                                padding: '8px 16px', 
                                                borderRadius: 'var(--radius-md)',
                                                border: '1px solid var(--success-main)'
                                            }}
                                        >
                                            {successMessage}
                                        </Typography>
                                    </Box>
                                )}
                                <AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="total">
                                            <Build />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{summaryData.implementationSupports}</Typography>
                                            <Typography variant="body2">Implementation Cost/Rate</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="active">
                                            <Storage />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{summaryData.dataMigrationSupports}</Typography>
                                            <Typography variant="body2">Data Migration</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="active">
                                            <IntegrationInstructions />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{summaryData.integrationSupports}</Typography>
                                            <Typography variant="body2">Interface Integration</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="inactive">
                                            <School />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{summaryData.trainingSupports}</Typography>
                                            <Typography variant="body2">Training Cost/Rate</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                                    <Button
                                        variant="outlined"
                                        startIcon={<SupportAgent />}
                                        onClick={handleOpenSupportModeDialog}
                                        sx={{ mr: 2 }}
                                    >
                                        Mode of Support
                                    </Button>
                                    <Button
                                        variant="contained"
                                        startIcon={<Add />}
                                        onClick={handleAdd}
                                    >
                                        Add Product Support
                                    </Button>
                                    {selectedIds.length > 0 && (
                                        <Button
                                            variant="outlined"
                                            color="error"
                                            startIcon={<Delete />}
                                            onClick={() => handleDelete(selectedIds)}
                                        >
                                            Delete Selected ({selectedIds.length})
                                        </Button>
                                    )}
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>

                        <AdminComponents.ControlsSection>
                            <AdminComponents.ControlsGroup>
                                <TextField
                                    variant="outlined"
                                    size="small"
                                    placeholder="Search product support..."
                                    value={searchTerm}
                                    onChange={(e) => handleSearch(e.target.value)}
                                    slotProps={{ input: { startAdornment: <Search color="disabled" /> } }}
                                />
                                <Button
                                    variant="outlined"
                                    startIcon={<FilterAlt />}
                                    onClick={() => { setIsSidebarOpen(true); setSidebarMode('search'); }}
                                    sx={{ mr: 2 }}
                                >
                                    Advanced Search
                                </Button>
                                <Button
                                    variant="outlined"
                                    startIcon={<Settings />}
                                    onClick={() => { setIsSidebarOpen(true); setSidebarMode('grid'); }}
                                    sx={{ mr: 2 }}
                                >
                                    Table Settings
                                </Button>
                            </AdminComponents.ControlsGroup>

                            <AdminComponents.ControlsGroup>
                                <AdminComponents.StyledToggleButtonGroup
                                    size="small"
                                    value={viewMode}
                                    exclusive
                                    onChange={(_, newView) => newView && setViewMode(newView)}
                                >
                                    <ToggleButton value="grid" title="Card View">
                                        <ViewModule />Card
                                    </ToggleButton>
                                    <ToggleButton value="compact" title="Compact View">
                                        <Apps />Compact
                                    </ToggleButton>
                                    <ToggleButton value="list" title="List View">
                                        <ViewList />List
                                    </ToggleButton>
                                    <ToggleButton value="table" title="Table View">
                                        <GridView />Table
                                    </ToggleButton>
                                </AdminComponents.StyledToggleButtonGroup>
                            </AdminComponents.ControlsGroup>
                        </AdminComponents.ControlsSection>

                        <AdminComponents.ContentBody>
                            <AdminComponents.MainLeftPane>
                                {renderContent()}
                            </AdminComponents.MainLeftPane>
                        </AdminComponents.ContentBody>
                    </AdminComponents.MainContentArea>
                </AdminComponents.AppBody>

                <Drawer
                   variant="persistent"
                   anchor="right"
                   open={isSidebarOpen}
                >
                  <AdminComponents.SidebarContainer>
                    <AdminComponents.SidebarHeader>
                      <Typography variant="h6">
                        {sidebarMode === 'search' ? 'Advanced Search' : 'Table Settings'}
                      </Typography>
                      <IconButton onClick={() => setIsSidebarOpen(false)}>
                        <Close />
                      </IconButton>
                    </AdminComponents.SidebarHeader>
                    <AdminComponents.SidebarContent>
                      {sidebarMode === 'search' && (
                        <>
                          <AdminComponents.SidebarSection>
                            <AdminComponents.SidebarSectionTitle>Filter Builder</AdminComponents.SidebarSectionTitle>
                            <FormControl fullWidth size="small">
                              <InputLabel>Field</InputLabel>
                              <Select value={filterBuilder.field} label="Field" onChange={e => setFilterBuilder(prev => ({ ...prev, field: e.target.value }))}>
                                {ALL_COLUMNS.map(col => <MenuItem key={col.key} value={col.key}>{col.label}</MenuItem>)}
                              </Select>
                            </FormControl>
                            <FormControl fullWidth size="small">
                              <InputLabel>Operator</InputLabel>
                              <Select value={filterBuilder.operator} label="Operator" onChange={e => setFilterBuilder(prev => ({ ...prev, operator: e.target.value }))}>
                                {['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'].map(op => <MenuItem key={op} value={op}>{op}</MenuItem>)}
                              </Select>
                            </FormControl>
                            <TextField label="Value" variant="outlined" size="small" fullWidth value={filterBuilder.value} onChange={e => setFilterBuilder(prev => ({ ...prev, value: e.target.value }))} />
                            <Button variant="outlined" fullWidth onClick={() => {
                              if (filterBuilder.field && filterBuilder.operator && filterBuilder.value) {
                                setStagedFilters([...stagedFilters, { ...filterBuilder, id: Date.now() }]);
                                setFilterBuilder({ field: '', operator: '', value: '' });
                              }
                            }}>Add Filter</Button>
                          </AdminComponents.SidebarSection>
                          <AdminComponents.SidebarSection>
                            <AdminComponents.SidebarSectionTitle>Staged Filters</AdminComponents.SidebarSectionTitle>
                            <AdminComponents.FilterChipContainer>
                              {stagedFilters.length > 0 ? stagedFilters.map(f => (
                                <Chip key={f.id} label={`${ALL_COLUMNS.find(c => c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setStagedFilters(stagedFilters.filter(sf => sf.id !== f.id))} />
                              )) : <Typography variant="body2" color="text.secondary">No filters staged.</Typography>}
                            </AdminComponents.FilterChipContainer>
                          </AdminComponents.SidebarSection>
                          <AdminComponents.SidebarSection>
                            <AdminComponents.SidebarSectionTitle>Active Filters</AdminComponents.SidebarSectionTitle>
                            <AdminComponents.FilterChipContainer>
                              {activeFilters.length > 0 ? activeFilters.map(f => (
                                <Chip key={f.id} label={`${ALL_COLUMNS.find(c => c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setActiveFilters(activeFilters.filter(af => af.id !== f.id))} />
                              )) : <Typography variant="body2" color="text.secondary">No filters active.</Typography>}
                            </AdminComponents.FilterChipContainer>
                          </AdminComponents.SidebarSection>
                        </>
                      )}
                      {sidebarMode === 'grid' && (
                        <>
                          <AdminComponents.SidebarSection>
                            <AdminComponents.SidebarSectionTitle>Visible Columns</AdminComponents.SidebarSectionTitle>
                            <AdminComponents.ColumnActionContainer>
                              <Button size="small" onClick={() => setColumnOrder(ALL_COLUMNS.map(c => c.key))}>Select All</Button>
                              <Button size="small" onClick={() => setColumnOrder(columnOrder.length > 1 ? [columnOrder[0]] : columnOrder)}>Deselect All</Button>
                            </AdminComponents.ColumnActionContainer>
                            <AdminComponents.ColumnVisibilityContainer>
                              {ALL_COLUMNS.map(col => (
                                <FormControlLabel
                                  key={col.key}
                                  control={<Checkbox checked={columnOrder.includes(col.key)} onChange={() => {
                                    const isVisible = columnOrder.includes(col.key);
                                    let newOrder;
                                    if (isVisible) {
                                      if (columnOrder.length > 1) {
                                        newOrder = columnOrder.filter(key => key !== col.key);
                                      } else {
                                        return;
                                      }
                                    } else {
                                      const originalKeys = ALL_COLUMNS.map(c => c.key);
                                      newOrder = originalKeys.filter(key => columnOrder.includes(key) || key === col.key);
                                    }
                                    setColumnOrder(newOrder);
                                  }} name={col.key} />}
                                  label={col.label}
                                />
                              ))}
                            </AdminComponents.ColumnVisibilityContainer>
                          </AdminComponents.SidebarSection>
                          <AdminComponents.SidebarSection>
                            <AdminComponents.SidebarSectionTitle>Group By</AdminComponents.SidebarSectionTitle>
                            <AdminComponents.FilterChipContainer>
                              {groupByKeys.length > 0 ? groupByKeys.map(key => (
                                <Chip
                                  key={key}
                                  label={ALL_COLUMNS.find(c => c.key === key)?.label}
                                  onDelete={() => handleGroupByChange(key)}
                                />
                              )) : <Typography variant="body2" color="text.secondary">None selected.</Typography>}
                            </AdminComponents.FilterChipContainer>
                            <AdminComponents.ColumnVisibilityContainer>
                              {ALL_COLUMNS.filter(c => c.groupable).map(col => (
                                <FormControlLabel
                                  key={col.key}
                                  control={
                                    <Checkbox
                                      checked={groupByKeys.includes(col.key)}
                                      onChange={() => handleGroupByChange(col.key)}
                                    />
                                  }
                                  label={col.label}
                                />
                              ))}
                            </AdminComponents.ColumnVisibilityContainer>
                          </AdminComponents.SidebarSection>
                        </>
                      )}
                    </AdminComponents.SidebarContent>
                    <AdminComponents.SidebarFooter>
                      {sidebarMode === 'search' && (
                        <>
                          <Button variant="outlined" onClick={() => { setStagedFilters([]); setActiveFilters([]); }}>Reset</Button>
                          <Button variant="contained" color="primary" onClick={() => { setActiveFilters([...activeFilters, ...stagedFilters]); setStagedFilters([]); }}>Apply</Button>
                        </>
                      )}
                      {sidebarMode === 'grid' && (
                        <Button variant="contained" fullWidth onClick={() => setIsSidebarOpen(false)}>Close</Button>
                      )}
                    </AdminComponents.SidebarFooter>
                  </AdminComponents.SidebarContainer>
                </Drawer>

                {/* Product Support Dialog */}
                <ProductSupportDialog
                    open={dialogOpen}
                    onClose={handleCloseDialog}
                    supportData={dialogSupport}
                    mode={dialogMode}
                    onSave={handleSaveDialog}
                />

                {/* Mode of Support Configuration Dialog */}
                <Dialog open={supportModeDialogOpen} onClose={handleCloseSupportModeDialog} fullWidth maxWidth="md">
                    <AdminComponents.DialogHeader>
                        <Typography variant="h6">
                            <SupportAgent sx={{ mr: 1, verticalAlign: 'middle' }} />
                            Mode of Support Configuration
                        </Typography>
                        <IconButton onClick={handleCloseSupportModeDialog} size="small">
                            <Close />
                        </IconButton>
                    </AdminComponents.DialogHeader>
                    <DialogContent>
                        <Box sx={{ p: 2 }}>
                            <Grid container spacing={4}>
                                {/* Support Days Section */}
                                <Grid item xs={12} md={4}>
                                    <Box sx={{ mb: 2 }}>
                                        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                            <CalendarToday sx={{ mr: 1 }} />
                                            Support Days
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                            Select the days when support will be available:
                                        </Typography>
                                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                                            {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map(day => (
                                                <FormControlLabel
                                                    key={day}
                                                    control={
                                                        <Checkbox
                                                            checked={supportDays.includes(day)}
                                                            onChange={() => handleDayToggle(day)}
                                                        />
                                                    }
                                                    label={day}
                                                />
                                            ))}
                                        </Box>
                                    </Box>
                                </Grid>

                                {/* Support Modes Section */}
                                <Grid item xs={12} md={4}>
                                    <Box sx={{ mb: 2 }}>
                                        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                            <SupportAgent sx={{ mr: 1 }} />
                                            Support Modes
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                            Select the available support channels:
                                        </Typography>
                                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                                            {['Chat', 'E-Mail', 'Phone'].map(mode => (
                                                <FormControlLabel
                                                    key={mode}
                                                    control={
                                                        <Checkbox
                                                            checked={supportModes.includes(mode)}
                                                            onChange={() => handleModeToggle(mode)}
                                                        />
                                                    }
                                                    label={mode}
                                                />
                                            ))}
                                        </Box>
                                    </Box>
                                </Grid>

                                {/* Support Timings Section */}
                                <Grid item xs={12} md={4}>
                                    <Box sx={{ mb: 2 }}>
                                        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                            <AccessTime sx={{ mr: 1 }} />
                                            Support Timings
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                            Set the support hours:
                                        </Typography>
                                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                                            <TextField
                                                label="Start Time"
                                                type="time"
                                                value={supportStartTime}
                                                onChange={(e) => setSupportStartTime(e.target.value)}
                                                fullWidth
                                                slotProps={{ inputLabel: { shrink: true } }}
                                                helperText={`Display: ${formatTime(supportStartTime)}`}
                                            />
                                            <TextField
                                                label="End Time"
                                                type="time"
                                                value={supportEndTime}
                                                onChange={(e) => setSupportEndTime(e.target.value)}
                                                fullWidth
                                                slotProps={{ inputLabel: { shrink: true } }}
                                                helperText={`Display: ${formatTime(supportEndTime)}`}
                                            />
                                        </Box>
                                    </Box>
                                </Grid>
                            </Grid>

                            {/* Configuration Summary */}
                            <Box sx={{ mt: 3, p: 2, backgroundColor: 'var(--background-paper)', borderRadius: 'var(--radius-md)' }}>
                                <Typography variant="h6" sx={{ mb: 2 }}>Configuration Summary</Typography>
                                <Grid container spacing={2}>
                                    <Grid item xs={12} sm={4}>
                                        <Typography variant="body2" color="text.secondary">Support Days:</Typography>
                                        <Typography variant="body2">
                                            {supportDays.length > 0 ? supportDays.join(', ') : 'None selected'}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} sm={4}>
                                        <Typography variant="body2" color="text.secondary">Support Modes:</Typography>
                                        <Typography variant="body2">
                                            {supportModes.length > 0 ? supportModes.join(', ') : 'None selected'}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} sm={4}>
                                        <Typography variant="body2" color="text.secondary">Support Hours:</Typography>
                                        <Typography variant="body2">
                                            {formatTime(supportStartTime)} - {formatTime(supportEndTime)}
                                        </Typography>
                                    </Grid>
                                </Grid>
                            </Box>
                        </Box>
                    </DialogContent>
                    <DialogActions sx={{ p: 2, borderTop: '1px solid var(--border-color)' }}>
                        <Button onClick={handleCloseSupportModeDialog} variant="outlined" startIcon={<Close />}>
                            Cancel
                        </Button>
                        <Button onClick={handleSaveSupportMode} variant="contained" startIcon={<Save />}>
                            Save Configuration
                        </Button>
                    </DialogActions>
                </Dialog>
            </AdminComponents.AppContainer>
        </ThemeProvider>
    );
};

export default Support;
