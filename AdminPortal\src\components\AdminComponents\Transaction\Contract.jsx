import React, { useState, useMemo, useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableCell, TableHead, TableRow, TableSortLabel,
    Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle,
    ToggleButton, ToggleButtonGroup, Avatar,
    List, ListItemText, FormControlLabel, Menu, Drawer, Chip, FormControl, InputLabel, Select, MenuItem
} from '@mui/material';
import { ThemeProvider, useTheme } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView as GridViewIcon, Apps, ViewList,
    BarChart, Warning, CheckCircle, Cancel, Schedule, Autorenew, Description,
    Show<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
} from '@mui/icons-material';
import { theme as appTheme, AdminComponents } from '../../../styles/theme'; // Using the central theme

// --- MOCK DATA ---
const initialContracts = [
    { id: 1, contractId: 'CTR-2024-001', customer: 'Prevost', clientType: 'Enterprise Client', type: 'LICENSE', startDate: '2025-01-01', endDate: '2026-01-01', renewalType: 'Automatic', noticePeriodDays: 90, internalOwner: 'Sarah Smith', value: '$850,000', status: 'Active' },
    { id: 2, contractId: 'CTR-2024-002', customer: 'Volvo', clientType: 'Mid-Market Client', type: 'SUPPORT', startDate: '2023-06-01', endDate: '2024-06-01', renewalType: 'Manual', noticePeriodDays: 0, internalOwner: 'IT Department', value: '$450,000', status: 'Expired' },
    { id: 3, contractId: 'CTR-2024-003', customer: 'Wirtgen', clientType: 'SMB Client', type: 'SERVICE', startDate: '2024-03-15', endDate: '2025-03-15', renewalType: 'Automatic', noticePeriodDays: 60, internalOwner: 'Legal Team', value: '$320,000', status: 'Active' },
    { id: 4, contractId: 'CTR-2024-004', customer: 'Yanmar', clientType: 'Enterprise Client', type: 'LICENSE', startDate: '2023-12-01', endDate: '2024-12-01', renewalType: 'Manual', noticePeriodDays: 0, internalOwner: 'Finance Department', value: '$1,200,000', status: 'Expiring' },
    { id: 5, contractId: 'CTR-2024-005', customer: 'Licolin', clientType: 'Enterprise Client', type: 'SUPPORT', startDate: '2023-09-01', endDate: '2024-09-01', renewalType: 'Automatic', noticePeriodDays: 120, internalOwner: 'Procurement', value: '$680,000', status: 'Active' },
];

const ALL_COLUMNS = [
    { key: 'contractId', label: 'Contract ID', type: 'string' },
    { key: 'customer', label: 'Customer', type: 'string' },
    { key: 'status', label: 'Status', type: 'string' },
    { key: 'type', label: 'Type', type: 'string' },
    { key: 'startDate', label: 'Start Date', type: 'date' },
    { key: 'endDate', label: 'End Date', type: 'date' },
    { key: 'value', label: 'Value', type: 'currency' },
    { key: 'internalOwner', label: 'Owner', type: 'string' },
    { key: 'renewalType', label: 'Renewal', type: 'string' },
    { key: 'noticePeriodDays', label: 'Notice (Days)', type: 'number' },
];

const FILTER_OPERATORS = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'];

// Configurations now pull colors directly from the theme
const STATUS_CONFIG = {
    Active: { status: 'Active' },
    Expiring: { status: 'Expiring' },
    Expired: { status: 'Expired' },
};

const TYPE_CONFIG = {
    LICENSE: { color: appTheme.palette.contractTypes.license.color, bgColor: appTheme.palette.contractTypes.license.bgColor },
    SUPPORT: { color: appTheme.palette.contractTypes.support.color, bgColor: appTheme.palette.contractTypes.support.bgColor },
    SERVICE: { color: appTheme.palette.contractTypes.service.color, bgColor: appTheme.palette.contractTypes.service.bgColor },
};

const getDaysLeft = (date) => 0; // Placeholder for date calculation logic

// --- UI COMPONENTS ---

const ActionButtons = ({ contract, onView, onEdit, onDelete, onRenew, isCollapsed }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);
    const handleClose = () => setAnchorEl(null);

    if (isCollapsed) {
        return (
            <Box onClick={e => e.stopPropagation()}>
                <IconButton size="small" onClick={(e) => setAnchorEl(e.currentTarget)}><MoreVert /></IconButton>
                <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
                    <AdminComponents.ActionMenuItem onClick={() => { onView(contract); handleClose(); }}><Visibility fontSize="small" /> View</AdminComponents.ActionMenuItem>
                    <AdminComponents.ActionMenuItem onClick={() => { onRenew(contract); handleClose(); }}><Autorenew fontSize="small" /> Renew</AdminComponents.ActionMenuItem>
                    <AdminComponents.ActionMenuItem onClick={() => { onEdit(contract); handleClose(); }}><Edit fontSize="small" /> Edit</AdminComponents.ActionMenuItem>
                    <AdminComponents.ActionMenuItem color="error" onClick={() => { onDelete([contract.id]); handleClose(); }}><Delete fontSize="small" /> Delete</AdminComponents.ActionMenuItem>
                </Menu>
            </Box>
        );
    }

    return (
        <Box onClick={e => e.stopPropagation()}>
            <IconButton size="small" onClick={() => onView(contract)} title="View Details"><Visibility fontSize="small" /></IconButton>
            <IconButton size="small" onClick={() => onRenew(contract)} title="Renew"><Autorenew fontSize="small" /></IconButton>
            <IconButton size="small" onClick={() => onEdit(contract)} title="Edit"><Edit fontSize="small" /></IconButton>
            <IconButton size="small" color="error" onClick={() => onDelete([contract.id])} title="Delete"><Delete fontSize="small" /></IconButton>
        </Box>
    );
};

const ContractCard = ({ contract, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView, onRenew, isGraphVisible }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox
            className="card-checkbox"
            checked={isChecked}
            onChange={() => onSelect(contract.id)}
            onClick={e => e.stopPropagation()}
        />
        <AdminComponents.CardActionContainer>
            <ActionButtons {...{ contract, onView, onEdit, onDelete, onRenew, isCollapsed: isGraphVisible }} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.PaddedCardContent>
            <Typography variant="h6" component="div" noWrap>{contract.customer}</Typography>
            <AdminComponents.IndustryTypography color="text.secondary">{contract.contractId}</AdminComponents.IndustryTypography>
            <AdminComponents.StatusBadge ownerState={STATUS_CONFIG[contract.status]} label={contract.status} size="small" />
            <AdminComponents.CardDivider />
            {ALL_COLUMNS.filter(col => !['contractId', 'customer', 'status'].includes(col.key)).map(col => (
                <AdminComponents.CardDetailRow key={col.key} variant="body2">
                    <strong>{col.label}:</strong> <span>{contract[col.key] || '-'}</span>
                </AdminComponents.CardDetailRow>
            ))}
        </AdminComponents.PaddedCardContent>
    </AdminComponents.CardBase>
);

const ContractCompactCard = ({ contract, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView, onRenew, isGraphVisible }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(contract.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer>
            <ActionButtons {...{ contract, onView, onEdit, onDelete, onRenew, isCollapsed: isGraphVisible }} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.CompactCardContent>
            <div>
                <Typography variant="subtitle1" fontWeight="bold" noWrap>{contract.customer}</Typography>
                <Typography variant="caption" color="text.secondary">{contract.contractId}</Typography>
            </div>
            <AdminComponents.CompactCardFooter>
                <Typography variant="body2" fontWeight="500">{contract.type}</Typography>
                <AdminComponents.StatusBadge ownerState={STATUS_CONFIG[contract.status]} label={contract.status} size="small" />
            </AdminComponents.CompactCardFooter>
        </AdminComponents.CompactCardContent>
    </AdminComponents.CardBase>
);

const ContractListItem = ({ contract, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView, onRenew, isGraphVisible }) => (
    <AdminComponents.ListItemCard isSelected={isSelected} onClick={onClick}>
        <AdminComponents.ListItemGrid>
            <Checkbox checked={isChecked} onChange={() => onSelect(contract.id)} />
            <Box>
                <Typography fontWeight="bold">{contract.customer}</Typography>
                <Typography variant="body2" color="text.secondary">{contract.contractId}</Typography>
            </Box>
            <Typography variant="body2">{contract.type}</Typography>
            <Typography variant="body2">{contract.value}</Typography>
            <AdminComponents.StatusBadge ownerState={STATUS_CONFIG[contract.status]} label={contract.status} size="small" />
            <AdminComponents.ListItemActions>
                <ActionButtons {...{ contract, onView, onEdit, onDelete, onRenew, isCollapsed: isGraphVisible }} />
            </AdminComponents.ListItemActions>
        </AdminComponents.ListItemGrid>
    </AdminComponents.ListItemCard>
);

const ContractTable = ({ contracts, onRowClick, onHeaderClick, sortColumn, sortDirection, selectedIds, onSelectAll, onSelectOne, columnOrder, setColumnOrder, addLog, onDelete, onEdit, onView, onRenew, isGraphVisible }) => {
    const dragItemIndex = useRef(null);
    const dragOverItemIndex = useRef(null);

    const renderCellContent = (item, colKey) => {
        const value = item[colKey];
        if (colKey === 'status') return <AdminComponents.StatusBadge ownerState={STATUS_CONFIG[value]} label={value} size="small" />;
        if (colKey === 'type') return <AdminComponents.StatusBadge ownerState={TYPE_CONFIG[value]} label={value} size="small" />;
        if (colKey === 'value') return <Typography variant="body2" fontWeight="bold">{value}</Typography>;
        if (colKey === 'daysToExpiry') {
            const days = getDaysLeft(item.endDate);
            if (days < 0) return <AdminComponents.StatusBadge ownerState={STATUS_CONFIG.Expired} label="Expired" size="small" />;
            if (days <= 30) return <Typography color="warning.dark">{days}</Typography>;
            return days;
        }
        return value || '-';
    };

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        addLog({ user: 'Admin', action: 'reordered columns' });
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };


    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < contracts.length} checked={contracts.length > 0 && selectedIds.length === contracts.length} onChange={onSelectAll} /></TableCell>
                        {columnOrder.map((colKey, index) => (
                            <AdminComponents.DraggableHeaderCell
                                key={colKey}
                                draggable
                                onDragStart={() => (dragItemIndex.current = index)}
                                onDragEnter={() => (dragOverItemIndex.current = index)}
                                onDrop={handleDrop}
                                onDragOver={(e) => e.preventDefault()}
                                sortDirection={sortColumn === colKey ? sortDirection : false}
                            >
                                <TableSortLabel active={sortColumn === colKey} direction={sortDirection} onClick={() => onHeaderClick(colKey)}>
                                    {ALL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </AdminComponents.DraggableHeaderCell>
                        ))}
                        <AdminComponents.ActionTableCell align="center">Actions</AdminComponents.ActionTableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {contracts.map(item => (
                        <TableRow key={item.id} hover onClick={() => onRowClick(item)}>
                            <TableCell padding="checkbox"><Checkbox checked={selectedIds.includes(item.id)} onChange={() => onSelectOne(item.id)} onClick={e => e.stopPropagation()} /></TableCell>
                            {columnOrder.map(colKey => (
                                <AdminComponents.ContentTableCell key={colKey}>{renderCellContent(item, colKey)}</AdminComponents.ContentTableCell>
                            ))}
                            <AdminComponents.ActionTableCell align="center">
                                <ActionButtons contract={item} onView={onView} onEdit={onEdit} onDelete={onDelete} onRenew={onRenew} isCollapsed={isGraphVisible} />
                            </AdminComponents.ActionTableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </AdminComponents.TableViewContainer>
    );
};

const ContractGraph = ({ contract, chartType }) => {
    const chartRef = useRef(null);
    const chartInstance = useRef(null);
    const theme = useTheme(); // Use the theme hook to access theme properties

    useEffect(() => {
        if (chartInstance.current) chartInstance.current.destroy();
        if (chartRef.current && contract) {
            const ctx = chartRef.current.getContext('2d');
            chartInstance.current = new Chart(ctx, {
                type: chartType,
                data: {
                    labels: ['Q1', 'Q2', 'Q3', 'Q4'],
                    datasets: [{
                        label: 'Contract Value (Mock)',
                        data: [Math.random() * 100000, Math.random() * 100000, Math.random() * 100000, Math.random() * 100000],
                        backgroundColor: chartType !== 'line' ? theme.palette.chart.backgrounds : theme.palette.primary.main,
                        borderColor: theme.palette.primary.dark,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { title: { display: true, text: `${contract.customer} - Contract Analysis` } },
                }
            });
        }
        return () => { if (chartInstance.current) chartInstance.current.destroy(); };
    }, [contract, chartType, theme]);

    return (
        <>
            {contract ? <AdminComponents.GraphContainer><canvas ref={chartRef}></canvas></AdminComponents.GraphContainer> : <AdminComponents.CenteredMessage><Typography>Select a contract to see graph</Typography></AdminComponents.CenteredMessage>}
        </>
    );
};

const ActivityLog = ({ logs }) => (
    <AdminComponents.ActivityLogPaper>
        <AdminComponents.ActivityLogTitle variant="h6" gutterBottom>
            Activity Log
        </AdminComponents.ActivityLogTitle>
        <AdminComponents.ActivityLogListContainer>
            <List disablePadding>
                {logs.map((log, index) => (
                    <AdminComponents.ActivityLogListItem key={index} disableGutters>
                        <AdminComponents.ActivityLogIconContainer>
                            <AdminComponents.ActivityLogAvatar>
                                <Schedule fontSize="small" />
                            </AdminComponents.ActivityLogAvatar>
                        </AdminComponents.ActivityLogIconContainer>
                        <ListItemText
                            primary={
                                <AdminComponents.ActivityLogTextContainer>
                                    <Typography variant="body2" component="span" color="text.secondary">
                                        <Typography component="span" fontWeight="bold" color="text.primary">{log.user}</Typography>
                                        {' '}{log.action}{' '}
                                        {log.target && <AdminComponents.ActivityLogLink component="a" href="#">{log.target}</AdminComponents.ActivityLogLink>}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                        {log.timestamp}
                                    </Typography>
                                </AdminComponents.ActivityLogTextContainer>
                            }
                        />
                    </AdminComponents.ActivityLogListItem>
                ))}
            </List>
        </AdminComponents.ActivityLogListContainer>
    </AdminComponents.ActivityLogPaper>
);

// --- MAIN WRAPPER COMPONENT ---
const ContractPage = () => (
    <ThemeProvider theme={appTheme}>
        <Contract />
    </ThemeProvider>
);


// --- MAIN APP COMPONENT ---
const Contract = () => {
    const [contracts, setContracts] = useState(initialContracts);
    const [selectedContract, setSelectedContract] = useState(null);
    const [modalState, setModalState] = useState({ isOpen: false, contract: null, isAdding: false });
    const [activityLog, setActivityLog] = useState([
        { user: 'Admin', action: 'Created a new view', target: 'Contract Expiry Overview', timestamp: '3/15/2023, 10:25:00 AM' },
        { user: 'Analyst', action: 'Updated a contract', target: 'CTR-2024-001', timestamp: '3/16/2023, 9:00:00 AM' },
    ]);
    const [filter, setFilter] = useState('all');
    const [viewMode, setViewMode] = useState('cards');
    const [sortColumn, setSortColumn] = useState('customer');
    const [sortDirection, setSortDirection] = useState('asc');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedIds, setSelectedIds] = useState([]);
    const [deleteConfirmation, setDeleteConfirmation] = useState({ isOpen: false, idsToDelete: [] });
    const [isGraphVisible, setIsGraphVisible] = useState(true);
    const [chartType, setChartType] = useState('bar');
    const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(c => c.key));
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [sidebarMode, setSidebarMode] = useState('search');
    const [stagedFilters, setStagedFilters] = useState([]);
    const [activeFilters, setActiveFilters] = useState([]);
    const [filterBuilder, setFilterBuilder] = useState({ field: '', operator: '', value: '' });
    const [groupByKeys, setGroupByKeys] = useState([]);
    // Quick Filter options (status and type)
    const quickFilterOptions = useMemo(() => {
        const statuses = [...new Set(contracts.map(c => c.status))];
        const types = [...new Set(contracts.map(c => c.type))];
        return [...statuses, ...types];
    }, [contracts]);
    const handleAddQuickFilter = (value) => {
        const statusValues = ['Active', 'Expiring', 'Expired'];
        const field = statusValues.includes(value) ? 'status' : 'type';
        const newFilter = { field, operator: 'Equals', value, id: Date.now() };
        setStagedFilters([...stagedFilters, newFilter]);
    };
    // Advanced Search Handlers
    const handleAddStagedFilter = () => {
        if (filterBuilder.field && filterBuilder.operator && filterBuilder.value) {
            setStagedFilters([...stagedFilters, { ...filterBuilder, id: Date.now() }]);
            setFilterBuilder({ field: '', operator: '', value: '' });
        }
    };
    const handleApplyFilters = () => {
        setActiveFilters([...activeFilters, ...stagedFilters]);
        setStagedFilters([]);
    };
    const handleResetFilters = () => {
        setStagedFilters([]);
        setActiveFilters([]);
    };
    const handleToggleSidebar = (mode) => {
        const willBeOpen = sidebarMode === mode ? !isSidebarOpen : true;
        setIsSidebarOpen(willBeOpen);
        setSidebarMode(mode);
    };
    const handleColumnVisibilityChange = (columnKey) => {
        const isVisible = columnOrder.includes(columnKey);
        let newOrder;
        if (isVisible) {
            if (columnOrder.length > 1) {
                newOrder = columnOrder.filter(key => key !== columnKey);
            } else {
                return;
            }
        } else {
            const originalKeys = ALL_COLUMNS.map(c => c.key);
            newOrder = originalKeys.filter(key => columnOrder.includes(key) || key === columnKey);
        }
        setColumnOrder(newOrder);
    };
    const handleGroupByChange = (key) => {
        setGroupByKeys(prev =>
            prev.includes(key) ? prev.filter(k => k !== key) : [...prev, key]
        );
    };

    const processedContracts = useMemo(() => {
        let current = contracts;
        if (filter !== 'all') current = current.filter(c => c.status.toLowerCase() === filter);
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            current = current.filter(c => c.customer.toLowerCase().includes(term) || c.contractId.toLowerCase().includes(term));
        }
        // Advanced filters
        if (activeFilters.length > 0) {
            current = current.filter(contract => {
                return activeFilters.every(filter => {
                    const { field, operator, value } = filter;
                    const contractValue = String(contract[field]).toLowerCase();
                    const filterValue = String(value).toLowerCase();
                    switch (operator) {
                        case 'Equals': return contractValue === filterValue;
                        case 'Not Equals': return contractValue !== filterValue;
                        case 'Contains': return contractValue.includes(filterValue);
                        case 'Starts With': return contractValue.startsWith(filterValue);
                        case 'Ends With': return contractValue.endsWith(filterValue);
                        default: return true;
                    }
                });
            });
        }
        // Sorting
        return [...current].sort((a, b) => {
            const valA = a[sortColumn], valB = b[sortColumn];
            if (valA === valB) return 0;
            if (typeof valA === 'string') return sortDirection === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
            return sortDirection === 'asc' ? valA - valB : valB - valA;
        });
    }, [contracts, filter, searchTerm, sortColumn, sortDirection, activeFilters]);

    const displayContract = useMemo(() => {
        const isSelectedVisible = processedContracts.some(c => c.id === selectedContract?.id);
        if (isSelectedVisible) return selectedContract;
        return processedContracts.length > 0 ? processedContracts[0] : null;
    }, [processedContracts, selectedContract]);

    const addLog = (logEntry) => {
        const timestamp = new Date().toLocaleString();
        setActivityLog(prev => [{ ...logEntry, timestamp }, ...prev].slice(0, 10));
    };

    const handleSaveContract = (contractToSave) => {
        addLog({ user: 'Admin', action: `Saved contract:`, target: contractToSave.contractId });
        setModalState({ isOpen: false, contract: null, isAdding: false });
    };
    const handleDeleteRequest = (ids) => setDeleteConfirmation({ isOpen: true, idsToDelete: ids });
    const confirmDelete = () => {
        addLog({ user: 'Admin', action: `Deleted ${deleteConfirmation.idsToDelete.length} contract(s).` });
        setDeleteConfirmation({ isOpen: false, idsToDelete: [] });
    };
    const handleShowDetails = (contract) => setSelectedContract(contract);
    const handleShowAddModal = () => setModalState({ isOpen: true, contract: { status: 'Active' }, isAdding: true });
    const handleSelectAll = (e) => setSelectedIds(e.target.checked ? processedContracts.map(c => c.id) : []);
    const handleSelectOne = (id) => setSelectedIds(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);

    const summaryStats = useMemo(() => ({
        total: { value: contracts.length, trend: "+8 this month" },
        active: { value: contracts.filter(c => c.status === 'Active').length, trend: "91% active rate" },
        expiring: { value: contracts.filter(c => c.status === 'Expiring').length, trend: "Next 30 days" },
        expired: { value: contracts.filter(c => c.status === 'Expired').length, trend: "+15% this quarter" },
    }), [contracts]);

    const renderCurrentView = () => (
        <AdminComponents.ViewContainer>
            {processedContracts.length > 0 ? (
                <>
                    {viewMode === 'cards' && <AdminComponents.GridView>{processedContracts.map(c => <ContractCard key={c.id} contract={c} onClick={() => setSelectedContract(c)} isSelected={selectedContract?.id === c.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(c.id)} onDelete={handleDeleteRequest} onEdit={() => setModalState({ isOpen: true, contract: c })} onView={handleShowDetails} onRenew={() => alert('Renew: ' + c.contractId)} isGraphVisible={isGraphVisible} />)}</AdminComponents.GridView>}
                    {viewMode === 'compact' && <AdminComponents.CompactView>{processedContracts.map(c => <ContractCompactCard key={c.id} contract={c} onClick={() => setSelectedContract(c)} isSelected={selectedContract?.id === c.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(c.id)} onDelete={handleDeleteRequest} onEdit={() => setModalState({ isOpen: true, contract: c })} onView={handleShowDetails} onRenew={() => alert('Renew: ' + c.contractId)} isGraphVisible={isGraphVisible} />)}</AdminComponents.CompactView>}
                    {viewMode === 'list' && <AdminComponents.ListView>{processedContracts.map(c => <ContractListItem key={c.id} contract={c} onClick={() => setSelectedContract(c)} isSelected={selectedContract?.id === c.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(c.id)} onDelete={handleDeleteRequest} onEdit={() => setModalState({ isOpen: true, contract: c })} onView={handleShowDetails} onRenew={() => alert('Renew: ' + c.contractId)} isGraphVisible={isGraphVisible} />)}</AdminComponents.ListView>}
                    {viewMode === 'table' && <ContractTable contracts={processedContracts} onRowClick={setSelectedContract} onHeaderClick={(col) => { setSortColumn(col); setSortDirection(d => d === 'asc' ? 'desc' : 'asc') }} sortColumn={sortColumn} sortDirection={sortDirection} selectedIds={selectedIds} onSelectAll={handleSelectAll} onSelectOne={handleSelectOne} columnOrder={columnOrder} setColumnOrder={setColumnOrder} addLog={addLog} onDelete={handleDeleteRequest} onEdit={(c) => setModalState({ isOpen: true, contract: c })} onView={handleShowDetails} onRenew={(c) => alert('Renew: ' + c.contractId)} isGraphVisible={isGraphVisible} />}
                </>
            ) : (
                <AdminComponents.CenteredMessage component={Paper}>
                    <AdminComponents.EmptyStateIcon />
                    <Typography variant="h6">No Matching Contracts</Typography>
                    <Typography color="text.secondary">Try adjusting your search term or filters.</Typography>
                </AdminComponents.CenteredMessage>
            )}
        </AdminComponents.ViewContainer>
    );

    return (
        <>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={isSidebarOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={isSidebarOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                <AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.SummaryCard isActive={filter === 'all'} onClick={() => setFilter('all')}>
                                        <AdminComponents.SummaryAvatar variant="total"><Description /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.total.value}</Typography><Typography variant="body2" color="text.secondary">Total Contracts</Typography><AdminComponents.SummaryTrend variant="caption" color="success">{summaryStats.total.trend}</AdminComponents.SummaryTrend></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'active'} onClick={() => setFilter('active')}>
                                        <AdminComponents.SummaryAvatar variant="active"><CheckCircle /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.active.value}</Typography><Typography variant="body2" color="text.secondary">Active</Typography><AdminComponents.SummaryTrend variant="caption" color="success">{summaryStats.active.trend}</AdminComponents.SummaryTrend></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'expiring'} onClick={() => setFilter('expiring')}>
                                        <AdminComponents.SummaryAvatar variant="warning"><Warning /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.expiring.value}</Typography><Typography variant="body2" color="text.secondary">Expiring</Typography><AdminComponents.SummaryTrend variant="caption" color="warning">{summaryStats.expiring.trend}</AdminComponents.SummaryTrend></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'expired'} onClick={() => setFilter('expired')}>
                                        <AdminComponents.SummaryAvatar variant="expired"><Cancel /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.expired.value}</Typography><Typography variant="body2" color="text.secondary">Expired</Typography><AdminComponents.SummaryTrend variant="caption" color="error">{summaryStats.expired.trend}</AdminComponents.SummaryTrend></Box>
                                    </AdminComponents.SummaryCard>
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                                    <Button variant="contained" startIcon={<Add />} onClick={handleShowAddModal}>Add Contract</Button>
                                    <Button variant="outlined" startIcon={<BarChart />} onClick={() => setIsGraphVisible(!isGraphVisible)}>Graphs</Button>
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>

                        <AdminComponents.ControlsSection>
                            <AdminComponents.ControlsGroup>
                                <TextField variant="outlined" size="small" placeholder="Search..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} InputProps={{ startAdornment: <Search color="disabled" /> }} />
                                <FormControlLabel control={<Checkbox onChange={handleSelectAll} checked={processedContracts.length > 0 && selectedIds.length === processedContracts.length} indeterminate={selectedIds.length > 0 && selectedIds.length < processedContracts.length} />} label="Select All" />
                                {selectedIds.length > 0 && <Button variant="outlined" color="error" startIcon={<Delete />} onClick={() => handleDeleteRequest(selectedIds)}>Delete ({selectedIds.length})</Button>}
                            </AdminComponents.ControlsGroup>
                            <AdminComponents.ControlsGroup>
                                <Button variant="outlined" startIcon={<FilterAlt />} onClick={() => handleToggleSidebar('search')}>Advanced Search</Button>
                                <Button variant="outlined" startIcon={<GridViewIcon />} onClick={() => handleToggleSidebar('grid')}>Table Settings</Button>
                                <AdminComponents.StyledToggleButtonGroup size="small" value={viewMode} exclusive onChange={(e, v) => v && setViewMode(v)}>
                                    <ToggleButton value="cards" title="Card View"><ViewModule />Card</ToggleButton>
                                    <ToggleButton value="compact" title="Compact View"><Apps />Compact</ToggleButton>
                                    <ToggleButton value="list" title="List View"><ViewList />List</ToggleButton>
                                    <ToggleButton value="table" title="Table View"><GridViewIcon />Table</ToggleButton>
                                </AdminComponents.StyledToggleButtonGroup>
                            </AdminComponents.ControlsGroup>
                        </AdminComponents.ControlsSection>

                        <AdminComponents.ContentBody>
                            <AdminComponents.MainLeftPane>{renderCurrentView()}</AdminComponents.MainLeftPane>
                            <AdminComponents.DetailsPane isCollapsed={!isGraphVisible}>
                                <AdminComponents.ChartTypeSelectorContainer>
                                    <AdminComponents.StyledToggleButtonGroup size="small" value={chartType} exclusive onChange={(e, v) => v && setChartType(v)} fullWidth>
                                        <ToggleButton value="bar"><BarChart />Bar</ToggleButton>
                                        <ToggleButton value="line"><ShowChart />Line</ToggleButton>
                                        <ToggleButton value="pie"><PieChart />Pie</ToggleButton>
                                        <ToggleButton value="doughnut"><DonutLarge />Donut</ToggleButton>
                                    </AdminComponents.StyledToggleButtonGroup>
                                </AdminComponents.ChartTypeSelectorContainer>
                                <ContractGraph contract={displayContract} chartType={chartType} />
                            </AdminComponents.DetailsPane>
                        </AdminComponents.ContentBody>

                        <ActivityLog logs={activityLog} />

                    </AdminComponents.MainContentArea>
                </AdminComponents.AppBody>

                <Drawer
                    variant="persistent"
                    anchor="right"
                    open={isSidebarOpen}
                >
                    <AdminComponents.SidebarContainer>
                        <AdminComponents.SidebarHeader>
                            <Typography variant="h6">
                                {sidebarMode === 'search' ? 'Advanced Search' : 'Table Settings'}
                            </Typography>
                            <IconButton onClick={() => setIsSidebarOpen(false)}>
                                <Cancel />
                            </IconButton>
                        </AdminComponents.SidebarHeader>
                        <AdminComponents.SidebarContent>
                            {sidebarMode === 'search' && (
                                <>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Quick Filters</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.QuickFilterContainer>
                                            {quickFilterOptions.map(opt => (
                                                <Chip key={opt} label={opt} onClick={() => handleAddQuickFilter(opt)} />
                                            ))}
                                        </AdminComponents.QuickFilterContainer>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Filter Builder</AdminComponents.SidebarSectionTitle>
                                        <FormControl fullWidth size="small">
                                            <InputLabel>Field</InputLabel>
                                            <Select value={filterBuilder.field} label="Field" onChange={e => setFilterBuilder(prev => ({ ...prev, field: e.target.value }))}>
                                                {ALL_COLUMNS.map(col => <MenuItem key={col.key} value={col.key}>{col.label}</MenuItem>)}
                                            </Select>
                                        </FormControl>
                                        <FormControl fullWidth size="small">
                                            <InputLabel>Operator</InputLabel>
                                            <Select value={filterBuilder.operator} label="Operator" onChange={e => setFilterBuilder(prev => ({ ...prev, operator: e.target.value }))}>
                                                {FILTER_OPERATORS.map(op => <MenuItem key={op} value={op}>{op}</MenuItem>)}
                                            </Select>
                                        </FormControl>
                                        <TextField label="Value" variant="outlined" size="small" fullWidth value={filterBuilder.value} onChange={e => setFilterBuilder(prev => ({ ...prev, value: e.target.value }))} />
                                        <Button variant="outlined" fullWidth onClick={handleAddStagedFilter}>Add Filter</Button>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Staged Filters</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.FilterChipContainer>
                                            {stagedFilters.length > 0 ? stagedFilters.map(f => (
                                                <Chip key={f.id} label={`${ALL_COLUMNS.find(c => c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setStagedFilters(stagedFilters.filter(sf => sf.id !== f.id))} />
                                            )) : <Typography variant="body2" color="text.secondary">No filters staged.</Typography>}
                                        </AdminComponents.FilterChipContainer>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Active Filters</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.FilterChipContainer>
                                            {activeFilters.length > 0 ? activeFilters.map(f => (
                                                <Chip key={f.id} label={`${ALL_COLUMNS.find(c => c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setActiveFilters(activeFilters.filter(af => af.id !== f.id))} />
                                            )) : <Typography variant="body2" color="text.secondary">No filters active.</Typography>}
                                        </AdminComponents.FilterChipContainer>
                                    </AdminComponents.SidebarSection>
                                </>
                            )}
                            {sidebarMode === 'grid' && (
                                <>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Visible Columns</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.ColumnActionContainer>
                                            <Button size="small" onClick={() => setColumnOrder(ALL_COLUMNS.map(c => c.key))}>Select All</Button>
                                            <Button size="small" onClick={() => setColumnOrder(columnOrder.length > 1 ? [columnOrder[0]] : columnOrder)}>Deselect All</Button>
                                        </AdminComponents.ColumnActionContainer>
                                        <AdminComponents.ColumnVisibilityContainer>
                                            {ALL_COLUMNS.map(col => (
                                                <FormControlLabel
                                                    key={col.key}
                                                    control={<Checkbox checked={columnOrder.includes(col.key)} onChange={() => handleColumnVisibilityChange(col.key)} name={col.key} />}
                                                    label={col.label}
                                                />
                                            ))}
                                        </AdminComponents.ColumnVisibilityContainer>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Group By</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.FilterChipContainer>
                                            {groupByKeys.length > 0 ? groupByKeys.map(key => (
                                                <Chip
                                                    key={key}
                                                    label={ALL_COLUMNS.find(c => c.key === key)?.label}
                                                    onDelete={() => handleGroupByChange(key)}
                                                />
                                            )) : <Typography variant="body2" color="text.secondary">None selected.</Typography>}
                                        </AdminComponents.FilterChipContainer>
                                        <AdminComponents.ColumnVisibilityContainer>
                                            {ALL_COLUMNS.map(col => (
                                                <FormControlLabel
                                                    key={col.key}
                                                    control={
                                                        <Checkbox
                                                            checked={groupByKeys.includes(col.key)}
                                                            onChange={() => handleGroupByChange(col.key)}
                                                        />
                                                    }
                                                    label={col.label}
                                                />
                                            ))}
                                        </AdminComponents.ColumnVisibilityContainer>
                                    </AdminComponents.SidebarSection>
                                </>
                            )}
                        </AdminComponents.SidebarContent>
                        <AdminComponents.SidebarFooter>
                            {sidebarMode === 'search' && (
                                <>
                                    <Button variant="outlined" onClick={handleResetFilters}>Reset</Button>
                                    <Button variant="contained" color="primary" onClick={handleApplyFilters}>Apply</Button>
                                </>
                            )}
                            {sidebarMode === 'grid' && (
                                <Button variant="contained" fullWidth onClick={() => setIsSidebarOpen(false)}>Close</Button>
                            )}
                        </AdminComponents.SidebarFooter>
                    </AdminComponents.SidebarContainer>
                </Drawer>

                <Dialog open={deleteConfirmation.isOpen} onClose={() => setDeleteConfirmation({ isOpen: false, idsToDelete: [] })}>
                    <DialogTitle>Confirm Deletion</DialogTitle>
                    <DialogContent><DialogContentText>Are you sure you want to delete {deleteConfirmation.idsToDelete.length} contract(s)? This action cannot be undone.</DialogContentText></DialogContent>
                    <DialogActions>
                        <Button onClick={() => setDeleteConfirmation({ isOpen: false, idsToDelete: [] })}>Cancel</Button>
                        <Button onClick={confirmDelete} color="error">Delete</Button>
                    </DialogActions>
                </Dialog>
            </AdminComponents.AppContainer>
        </>
    );
};

export default ContractPage;
