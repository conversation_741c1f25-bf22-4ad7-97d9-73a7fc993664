# This is a basic workflow that is manually triggered

name: Build Multi-Tenant_AdminPortal
on:
  workflow_dispatch:

jobs:
  build:
    runs-on: self-hosted

    env:      
      ZIP_FILE_NAME: multitenant_admin_portal.zip
      ZIP_FILE_CONTENT: appspec.yml create-iis-application.ps1 Prevost_Login Prevost_MainPage
      AWS_REGION: ap-south-1

    steps:
    - name: Checkout repository
      uses: actions/checkout@v2

    - name: Create source directory and move artifacts
      shell: powershell
      run: |
        mkdir source
        $env:ZIP_FILE_CONTENT.Split(" ") | ForEach-Object {
          $item = $_
          Write-Host "Processing: $item"
          if (Test-Path $item -PathType Container) {
            Copy-Item -Path $item -Destination "source" -Recurse -Force
          } elseif (Test-Path $item) {
            Copy-Item -Path $item -Destination "source" -Force
          } else {
            Write-Host "Skipping: $item (not found)"
          }
        }

    - name: Create ZIP file
      shell: powershell
      run: |
        Compress-Archive -Path "source", "appspec.yml", "create-iis-application.ps1" -DestinationPath "./$env:ZIP_FILE_NAME"

    - name: Verify files
      shell: powershell
      run: |
        mkdir ExtractedFiles
        Expand-Archive -Path "./$env:ZIP_FILE_NAME" -DestinationPath "./ExtractedFiles"
        dir ./ExtractedFiles
        dir ./ExtractedFiles/source/
        
    - name: Publish artifact to S3
      shell: powershell
      run: |
        aws s3 cp $env:ZIP_FILE_NAME s3://pamp-quest-deploy-input/staging/multitenant_admin_portal/

