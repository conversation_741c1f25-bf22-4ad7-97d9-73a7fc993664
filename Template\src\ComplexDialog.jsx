import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import <PERSON><PERSON><PERSON><PERSON> from 'jszip';
import { saveAs } from 'file-saver';
import './ComplexDialogIndex.css';

// --- Code Generation Utilities for ComplexDialog ---
const generateDialogComponentJsx = (styles) => {
  const tabsJson = JSON.stringify(styles.tabs, null, 4);

  return `
import React, { useState, useEffect } from 'react';
import './ConfiguredDialog.css';

// This component shows the configured dialog.
// Pass 'isOpen' and 'onClose' props to control its visibility.
const ConfiguredDialog = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  const initialTabs = ${tabsJson};
  const [activeTabId, setActiveTabId] = useState(initialTabs.length > 0 ? initialTabs[0].id : null);
  
  useEffect(() => {
    // Ensure activeTabId is valid if tabs change
    if (initialTabs.length > 0 && !initialTabs.find(t => t.id === activeTabId)) {
      setActiveTabId(initialTabs[0].id);
    }
  }, [initialTabs, activeTabId]);

  const handleOverlayClick = (e) => {
    // Clicks on the dialog itself should not close it
    if (e.target === e.currentTarget && ${styles.closeOnOverlayClick}) {
      onClose();
    }
  };
  
  const activeTab = initialTabs.find(t => t.id === activeTabId);

  return (
    <div className="dialog-overlay" onClick={handleOverlayClick}>
      <div className="configured-dialog">
        {${styles.showHeader} && (
          <header className="dialog-header">
            <div className="dialog-header-content">
              <h2 className="header-title">${styles.headerContent}</h2>
            </div>
            <button className="dialog-close-btn" onClick={onClose} aria-label="Close dialog">
              <span className="material-icons">close</span>
            </button>
          </header>
        )}

        {${styles.showTabs && styles.tabs.length > 0} && (
          <div className="dialog-tab-bar">
            {initialTabs.map(tab => (
              <button
                key={tab.id}
                className={\`dialog-tab-btn \${tab.id === activeTabId ? 'active' : ''}\`}
                onClick={() => setActiveTabId(tab.id)}
              >
                {tab.title}
              </button>
            ))}
          </div>
        )}

        <div className="dialog-content">
          {activeTab ? (
            <p>Content for {activeTab.title} goes here.</p>
          ) : (
            <p>Main dialog content goes here.</p>
          )}
        </div>

        {${styles.showFooter} && (
          <footer className="dialog-footer">
            <div className="footer-content-text">${styles.footerContent}</div>
            <div className="footer-buttons">
              <button className="footer-btn secondary" onClick={onClose}>Cancel</button>
              <button className="footer-btn primary">Confirm</button>
            </div>
          </footer>
        )}
      </div>
    </div>
  );
};

export default ConfiguredDialog;
`;
};

const generateDialogComponentCss = (styles) => {
  return `
/* ConfiguredDialog.css */
@import url('https://fonts.googleapis.com/icon?family=Material+Icons');

/* --- Dialog Overlay --- */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

/* --- Main Dialog Container --- */
.configured-dialog {
  width: ${styles.dialogWidth}${styles.dialogWidthUnit};
  height: ${styles.dialogHeight}${styles.dialogHeightUnit};
  background-color: ${styles.bgColor};
  color: ${styles.textColor};
  border: ${styles.borderThickness}px solid ${styles.borderColor};
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0,0,0,0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-width: 95vw;
  max-height: 95vh;
}

/* --- Dialog Header --- */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: ${styles.headerColor};
  height: ${styles.headerHeight ? `${styles.headerHeight}px` : 'auto'};
  flex-shrink: 0;
  color: white; /* Assuming light text on a dark header */
  text-align: ${styles.headerAlign};
}
.header-title { margin: 0; font-size: 1.25rem; }
.dialog-close-btn { background: none; border: none; color: white; cursor: pointer; opacity: 0.7; }
.dialog-close-btn:hover { opacity: 1; }

/* --- Tab Bar --- */
.dialog-tab-bar {
  display: flex;
  flex-shrink: 0;
  border-bottom: 1px solid ${styles.lineColor};
  background-color: #f8fafc; /* A slightly off-white background */
}
.dialog-tab-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  background: none;
  font-size: 0.9rem;
  font-weight: 500;
  color: #64748b; /* text-muted */
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
  flex-grow: ${styles.tabWidthMode === 'fill' ? '1' : '0'};
  flex-shrink: ${styles.tabWidthMode === 'fixed' ? '0' : '1'};
  width: ${styles.tabWidthMode === 'fixed' ? `${styles.tabFixedWidth}px` : 'auto'};
}
.dialog-tab-btn:hover { color: ${styles.headerColor}; }
.dialog-tab-btn.active { color: ${styles.headerColor}; border-bottom-color: ${styles.headerColor}; font-weight: 600; }

/* --- Dialog Content --- */
.dialog-content {
  flex-grow: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

/* --- Dialog Footer --- */
.dialog-footer {
  flex-shrink: 0;
  padding: 1rem 1.5rem;
  background-color: ${styles.footerColor};
  height: ${styles.footerHeight ? `${styles.footerHeight}px` : 'auto'};
  border-top: 1px solid ${styles.lineColor};
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}
.footer-content-text {
  flex-grow: 1;
  text-align: ${styles.footerAlign};
}
.footer-buttons { display: flex; gap: 0.5rem; }
.footer-btn { padding: 0.5rem 1.25rem; border-radius: 4px; border: 1px solid transparent; font-weight: 500; cursor: pointer; }
.footer-btn.primary { background-color: ${styles.headerColor}; color: white; }
.footer-btn.secondary { background-color: white; color: #334155; border-color: #cbd5e1; }
`;
};

const CodeExportModal = ({ isOpen, onClose, componentName, jsxCode, cssCode }) => {
    if (!isOpen) return null;

    const [activeTab, setActiveTab] = useState('jsx');

    const handleCopyToClipboard = (code) => {
        navigator.clipboard.writeText(code).then(() => alert('Code copied to clipboard!'), () => alert('Failed to copy code.'));
    };
    
    const generateReadmeContent = (name) => {
      const today = new Date().toLocaleDateString('en-CA'); // YYYY-MM-DD
      const folderName = `my-${name.toLowerCase()}-library`;
      
      return `
# ${name} Component

This component was generated by the HCLSoftware AfterMarket Web Templates application on ${today}.

It is a self-contained, configurable Dialog (Modal) component ready to be used in any React project.

---

## 1. Installation

Follow these steps to integrate the component into your project:

1.  **Place the Folder**: Move this entire folder (\`${folderName}\`) into your project's source directory. A common location is \`src/components/\`.

2.  **Check Dependencies**: This component requires \`react\` and \`react-dom\` to be present in your main project. The component's CSS also uses an \`@import\` rule for Google's Material Icons. For the "close" icon to display correctly, your project will need an internet connection to fetch the font.

---

## 2. Usage

Once the component is in your project, you can import it and render it. You must control its visibility (its "open" or "closed" state) from a parent component.

Here is a typical usage example in a parent component like \`App.js\`:

\`\`\`javascript
import React, { useState } from 'react';
import ${name} from './components/${folderName}'; // Adjust the import path as needed

function App() {
  // Create a state variable to control the dialog's visibility
  const [isDialogOpen, setDialogOpen] = useState(false);

  return (
    <div>
      <h1>My Application</h1>
      <button onClick={() => setDialogOpen(true)}>
        Show Dialog
      </button>

      {/* Render the dialog and pass the required props */}
      <${name}
        isOpen={isDialogOpen}
        onClose={() => setDialogOpen(false)}
      />
    </div>
  );
}

export default App;
\`\`\`

---

## 3. Component Props

| Prop      | Type       | Required | Description                                             |
|-----------|------------|----------|---------------------------------------------------------|
| \`isOpen\`  | \`Boolean\`  | Yes      | Controls whether the dialog is visible or hidden.       |
| \`onClose\` | \`Function\` | Yes      | A callback function that is triggered to close the dialog. |

---

## 4. Publishing to NPM (Optional)

If you wish to share this component as a public or private NPM package, you can publish it directly from this folder.

1.  **Login to NPM**: Open your terminal *inside this folder* and run \`npm login\`.
2.  **Publish**: Once logged in, run the command \`npm publish\`.

The \`package.json\` file is already configured with basic information. You may want to edit it to add your own author details, repository link, etc., before publishing.
`;
    };

    const handleDownloadZip = () => {
        const zip = new JSZip();
        const folderName = `my-${componentName.toLowerCase()}-library`;
        const folder = zip.folder(folderName);
        const readmeContent = generateReadmeContent(componentName);

        folder.file(`${componentName}.jsx`, jsxCode);
        folder.file(`ConfiguredDialog.css`, cssCode);
        folder.file('index.js', `import ${componentName} from './${componentName}';\nexport default ${componentName};`);
        folder.file('package.json', JSON.stringify({
            name: folderName,
            version: '1.0.0',
            description: `A configurable ${componentName} component generated from the template builder.`,
            main: 'index.js',
            peerDependencies: { "react": ">=16.8.0", "react-dom": ">=16.8.0" },
            license: "ISC"
        }, null, 2));
        folder.file('README.md', readmeContent);

        zip.generateAsync({ type: 'blob' }).then((content) => {
            saveAs(content, `${folderName}.zip`);
        });
    };

    return (
        <div className="export-modal-overlay" onClick={onClose}>
            <div className="export-modal-content" onClick={e => e.stopPropagation()}>
                <div className="export-modal-header">
                    <h3>Export: {componentName}</h3>
                    <button className="close-btn" onClick={onClose}><span className="material-icons">close</span></button>
                </div>
                <div className="export-modal-body">
                    <div className="tab-buttons">
                        <button className={activeTab === 'jsx' ? 'active' : ''} onClick={() => setActiveTab('jsx')}>React Component (JSX)</button>
                        <button className={activeTab === 'css' ? 'active' : ''} onClick={() => setActiveTab('css')}>CSS</button>
                    </div>
                    <div className="code-display-container">
                        <pre><code>{activeTab === 'jsx' ? jsxCode : cssCode}</code></pre>
                        <button className="copy-btn" onClick={() => handleCopyToClipboard(activeTab === 'jsx' ? jsxCode : cssCode)}>Copy</button>
                    </div>
                </div>
                <div className="export-modal-footer">
                    <button className="btn btn-secondary" onClick={onClose}>Close</button>
                    <button className="btn btn-primary" onClick={handleDownloadZip}>Download as ZIP</button>
                </div>
            </div>
        </div>
    );
};

const defaultDialogStyles = {
    closeOnOverlayClick: true, dialogWidth: 85, dialogWidthUnit: '%', dialogHeight: 85,
    dialogHeightUnit: '%', bgColor: '#ffffff', textColor: '#1e293b', borderThickness: 1,
    borderColor: '#e2e8f0', lineColor: '#E2E8F0', showHeader: true, headerContent: 'Dialog Header Title',
    headerAlign: 'left', headerColor: '#00B4D8', headerHeight: 120, showFooter: true,
    footerContent: 'Dialog Footer Content', footerAlign: 'center', footerColor: '#ffffff',
    footerHeight: 60, showTabs: true, tabWidthMode: 'auto', tabFixedWidth: 120,
    tabs: [ { id: 1, title: 'Details' }, { id: 2, title: 'Activity' }, { id: 3, title: 'History' } ]
};

const WireframeSidebar = () => <nav className="wireframe-sidebar"></nav>;

const SettingsDrawer = ({ isOpen, onClose, dialogStyles, setDialogStyles, handleStyleChange, handleNumericStyleChange, resetSettings, saveSettings, onExport }) => {
    const handleTabChange = (index, value) => {
        const newTabs = [...dialogStyles.tabs];
        newTabs[index].title = value;
        setDialogStyles(prev => ({ ...prev, tabs: newTabs }));
    };
    const addTab = () => setDialogStyles(prev => ({ ...prev, tabs: [...prev.tabs, { id: Date.now(), title: `New Tab` }] }));
    const removeTab = (idToRemove) => setDialogStyles(prev => ({ ...prev, tabs: prev.tabs.filter(tab => tab.id !== idToRemove) }));

    return (
        <div className={`settings-drawer ${isOpen ? 'open' : ''}`}>
            <div className="drawer-content-wrapper">
                <div className="drawer-header">
                    <h3>Dialog Settings</h3>
                    <button className="close-btn" onClick={onClose} aria-label="Close settings"><span className="material-icons">close</span></button>
                </div>
                <div className="settings-panel">
                    <div className="settings-section"><h4 className="settings-section-title">Dialog Properties</h4><div className="settings-grid"><div className="settings-control"><label htmlFor="dialogWidth">Dialog Width:</label><input type="number" id="dialogWidth" value={dialogStyles.dialogWidth || ''} onChange={handleNumericStyleChange} min="10"/></div><div className="settings-control"><label htmlFor="dialogWidthUnit">Unit:</label><select id="dialogWidthUnit" value={dialogStyles.dialogWidthUnit} onChange={handleStyleChange}><option value="%">%</option><option value="px">px</option></select></div><div className="settings-control"><label htmlFor="dialogHeight">Dialog Height:</label><input type="number" id="dialogHeight" value={dialogStyles.dialogHeight || ''} onChange={handleNumericStyleChange} min="10"/></div><div className="settings-control"><label htmlFor="dialogHeightUnit">Unit:</label><select id="dialogHeightUnit" value={dialogStyles.dialogHeightUnit} onChange={handleStyleChange}><option value="%">%</option><option value="px">px</option></select></div><div className="settings-control checkbox-control full-width"><input type="checkbox" id="closeOnOverlayClick" checked={dialogStyles.closeOnOverlayClick} onChange={handleStyleChange} /><label htmlFor="closeOnOverlayClick">Close Dialog on Outside Click</label></div></div></div>
                    <div className="settings-section"><h4 className="settings-section-title">Content Properties</h4><div className="settings-grid"><div className="settings-control"><label htmlFor="bgColor">Content BG Color:</label><input type="color" id="bgColor" value={dialogStyles.bgColor} onChange={handleStyleChange} /></div><div className="settings-control"><label htmlFor="textColor">Content Text Color:</label><input type="color" id="textColor" value={dialogStyles.textColor} onChange={handleStyleChange} /></div><div className="settings-control"><label htmlFor="borderThickness">Border (px):</label><input type="number" id="borderThickness" value={dialogStyles.borderThickness} onChange={handleNumericStyleChange} min="0" max="10" /></div><div className="settings-control"><label htmlFor="borderColor">Border Color:</label><input type="color" id="borderColor" value={dialogStyles.borderColor} onChange={handleStyleChange} /></div><div className="settings-control full-width"><label htmlFor="lineColor">Line Color:</label><input type="color" id="lineColor" value={dialogStyles.lineColor} onChange={handleStyleChange} /></div></div></div>
                    <div className="settings-section"><div className="settings-section-header"><h4 className="settings-section-title">Header Properties</h4><div className="settings-control checkbox-control"><input type="checkbox" id="showHeader" checked={dialogStyles.showHeader} onChange={handleStyleChange}/><label htmlFor="showHeader">Show Header</label></div></div><div className="settings-grid"><div className="settings-control full-width"><label htmlFor="headerContent">Header Text:</label><input type="text" id="headerContent" placeholder="Dialog Header" value={dialogStyles.headerContent} onChange={handleStyleChange} disabled={!dialogStyles.showHeader}/></div><div className="settings-control"><label htmlFor="headerHeight">Height (px):</label><input type="number" id="headerHeight" placeholder="Auto" min="0" value={dialogStyles.headerHeight || ''} onChange={handleNumericStyleChange} disabled={!dialogStyles.showHeader}/></div><div className="settings-control"><label htmlFor="headerColor">BG Color:</label><input type="color" id="headerColor" value={dialogStyles.headerColor} onChange={handleStyleChange} disabled={!dialogStyles.showHeader}/></div></div></div>
                    <div className="settings-section"><div className="settings-section-header"><h4 className="settings-section-title">Tab Properties</h4><div className="settings-control checkbox-control"><input type="checkbox" id="showTabs" checked={dialogStyles.showTabs} onChange={handleStyleChange} /><label htmlFor="showTabs">Show Tabs</label></div></div><div style={{ opacity: dialogStyles.showTabs ? 1 : 0.5 }}><div className="settings-grid" style={{ marginBottom: '1rem' }}><div className="settings-control full-width"><label htmlFor="tabWidthMode">Tab Width Behavior:</label><select id="tabWidthMode" value={dialogStyles.tabWidthMode} onChange={handleStyleChange} disabled={!dialogStyles.showTabs}><option value="auto">Responsive (Auto)</option><option value="fill">Fill Available Space</option><option value="fixed">Fixed Width</option></select></div>{dialogStyles.tabWidthMode === 'fixed' && (<div className="settings-control full-width"><label htmlFor="tabFixedWidth">Tab Fixed Width (px):</label><input type="number" id="tabFixedWidth" value={dialogStyles.tabFixedWidth || ''} onChange={handleNumericStyleChange} disabled={!dialogStyles.showTabs} min="20"/></div>)}</div><div className="tab-editor-container"><h5 className="settings-section-title" style={{marginBottom: 0}}>Manage Tabs</h5>{dialogStyles.tabs.map((tab, index) => (<div key={tab.id} className="tab-editor-row"><div className="settings-control" style={{flexGrow: 1}}><input type="text" placeholder="Tab Title" value={tab.title} onChange={(e) => handleTabChange(index, e.target.value)} disabled={!dialogStyles.showTabs} /></div><button className="btn-remove-tab" onClick={() => removeTab(tab.id)} title="Remove Tab" disabled={!dialogStyles.showTabs}><span className="material-icons">delete_outline</span></button></div>))}<button className="btn-add-tab" onClick={addTab} disabled={!dialogStyles.showTabs}><span className="material-icons">add</span> Add Tab</button></div></div></div>
                    <div className="settings-section"><div className="settings-section-header"><h4 className="settings-section-title">Footer Properties</h4><div className="settings-control checkbox-control"><input type="checkbox" id="showFooter" checked={dialogStyles.showFooter} onChange={handleStyleChange}/><label htmlFor="showFooter">Show Footer</label></div></div><div className="settings-grid"><div className="settings-control full-width"><label htmlFor="footerContent">Footer Text:</label><input type="text" id="footerContent" placeholder="Dialog Footer" value={dialogStyles.footerContent} onChange={handleStyleChange} disabled={!dialogStyles.showFooter}/></div><div className="settings-control"><label htmlFor="footerHeight">Height (px):</label><input type="number" id="footerHeight" placeholder="Auto" min="0" value={dialogStyles.footerHeight || ''} onChange={handleNumericStyleChange} disabled={!dialogStyles.showFooter}/></div><div className="settings-control"><label htmlFor="footerColor">BG Color:</label><input type="color" id="footerColor" value={dialogStyles.footerColor} onChange={handleStyleChange} disabled={!dialogStyles.showFooter}/></div></div></div>
                </div>
                <div className="drawer-actions">
                    <button className="btn btn-export" onClick={onExport}><span className="material-icons">code</span>Export</button>
                    <button className="btn btn-secondary" onClick={resetSettings}><span className="material-icons">restart_alt</span>Reset</button>
                    <button className="btn btn-primary" onClick={saveSettings}><span className="material-icons">save</span>Save</button>
                </div>
            </div>
        </div>
    );
};

const DialogComponent = ({ styles, onClose }) => {
    const [activeTabId, setActiveTabId] = useState(styles.tabs.length > 0 ? styles.tabs[0].id : null);
    useEffect(() => { if (!styles.tabs.find(t => t.id === activeTabId) && styles.tabs.length > 0) setActiveTabId(styles.tabs[0].id); }, [styles.tabs, activeTabId]);
    const handleOverlayClick = () => { if (styles.closeOnOverlayClick) onClose(); };
    const dialogContainerStyle = { width: `${styles.dialogWidth}${styles.dialogWidthUnit}`, height: `${styles.dialogHeight}${styles.dialogHeightUnit}`, backgroundColor: styles.bgColor, color: styles.textColor, border: `${styles.borderThickness}px solid ${styles.borderColor}` };
    const headerStyle = { backgroundColor: styles.headerColor, height: styles.headerHeight ? `${styles.headerHeight}px` : undefined };
    const footerStyle = { backgroundColor: styles.footerColor, height: styles.footerHeight ? `${styles.footerHeight}px` : undefined };
    const activeTab = styles.tabs.find(t => t.id === activeTabId);
    const getTabStyle = () => {
        if (styles.tabWidthMode === 'fill') return { flex: '1 1 0px', textAlign: 'center' };
        if (styles.tabWidthMode === 'fixed') return { width: `${styles.tabFixedWidth}px`, flexShrink: 0 };
        return {};
    }
    const tabStyle = getTabStyle();

    return (
        <div className="dialog-overlay" onClick={handleOverlayClick}>
            <div className="complex-dialog" style={dialogContainerStyle} onClick={(e) => e.stopPropagation()}>
                {styles.showHeader && (<header className="dialog-header" style={headerStyle}><div className="dialog-header-content"><div className="header-title-placeholder">{styles.headerContent}</div><div className="header-tabs-container"><div className="header-tab"></div><div className="header-tab"></div><div className="header-tab"></div></div></div><button className="dialog-close-btn" onClick={onClose}><span className="material-icons">close</span></button></header>)}
                {styles.showTabs && styles.tabs.length > 0 && (<div className={`dialog-tab-bar mode-${styles.tabWidthMode}`}>{styles.tabs.map(tab => (<button key={tab.id} className={`dialog-tab-btn ${tab.id === activeTabId ? 'active' : ''}`} onClick={() => setActiveTabId(tab.id)} style={tabStyle}>{tab.title}</button>))}</div>)}
                <div className="dialog-content">{styles.showTabs && activeTab ? (<h3>Content for {activeTab.title}</h3>) : (<><div className="content-line-placeholder" style={{ backgroundColor: styles.lineColor }}></div><div className="content-line-placeholder short" style={{ backgroundColor: styles.lineColor }}></div></>)}</div>
                {styles.showFooter && (<footer className="dialog-footer" style={footerStyle}><div className="footer-text-placeholder">{styles.footerContent}</div><div className="footer-buttons-container"><div className="footer-button-placeholder"></div><div className="footer-button-placeholder"></div></div></footer>)}
            </div>
        </div>
    );
};

function ComplexDialog() {
    const navigate = useNavigate();
    const [dialogStyles, setDialogStyles] = useState({ ...defaultDialogStyles });
    const [isDrawerOpen, setIsDrawerOpen] = useState(false);
    const [isFabMenuOpen, setIsFabMenuOpen] = useState(false);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    
    const [isExportModalOpen, setExportModalOpen] = useState(false);
    const [generatedCode, setGeneratedCode] = useState({ jsx: '', css: '' });

    const handleExport = () => {
        const jsx = generateDialogComponentJsx(dialogStyles);
        const css = generateDialogComponentCss(dialogStyles);
        setGeneratedCode({ jsx, css });
        setExportModalOpen(true);
    };

    const handleGoBack = () => navigate(-1);
    const handleStyleChange = (e) => {
        const { id, value, type, checked } = e.target;
        setDialogStyles(prev => ({ ...prev, [id]: type === 'checkbox' ? checked : value }));
    };
    const handleNumericStyleChange = (e) => {
        const { id, value } = e.target;
        setDialogStyles(prev => ({ ...prev, [id]: value === '' ? null : parseInt(value, 10) }));
    };
    const toggleDrawer = () => setIsDrawerOpen(!isDrawerOpen);
    const resetSettings = () => setDialogStyles({ ...defaultDialogStyles });
    const saveSettings = () => toggleDrawer();

    useEffect(() => { document.body.className = 'light-theme'; }, []);

    return (
        <div className="app-container">
            <div className={`app-body ${isDrawerOpen ? 'settings-drawer-is-open' : ''}`}>
                <header className="app-header">
                    <div className="header-left">
                        <button className="back-button" aria-label="Go back" onClick={handleGoBack}><span className="material-icons">arrow_back</span></button>
                        <h1 className="page-title">Dialog Configurator</h1>
                    </div>
                    <div className="header-right">
                        <button className="btn btn-secondary" onClick={() => setIsDialogOpen(true)}><span className="material-icons">open_in_new</span>Show Dialog</button>
                        <button className="btn btn-secondary" onClick={toggleDrawer}><span className="material-icons">settings</span>Settings</button>
                        <button className="btn btn-primary"><span className="material-icons">add</span>Add New</button>
                    </div>
                </header>
                <div className="main-content-area">
                    <WireframeSidebar />
                    <div className="content-scroll-wrapper">
                        <main className="main-content">
                            <div className="controls-section"><div className="search-filters"></div></div>
                            <div className="main-content-placeholder"><p>This is the main content area. <br/> Use the 'Show Dialog' button to open and configure the dialog.</p></div>
                        </main>
                    </div>
                </div>
                 <div className="fab-container" onMouseEnter={() => setIsFabMenuOpen(true)} onMouseLeave={() => setIsFabMenuOpen(false)}>
                    <div className={`fab-options ${isFabMenuOpen ? 'open' : ''}`}>
                        <button className="fab-option-btn" title="Upload" onClick={() => alert('Upload clicked')}><span className="material-icons">publish</span></button>
                        <button className="fab-option-btn" title="Edit" onClick={() => alert('Edit clicked')}><span className="material-icons">edit</span></button>
                        <button className="fab-option-btn" title="Create" onClick={() => alert('Create clicked')}><span className="material-icons">add_circle</span></button>
                    </div>
                    <button id="mainFab" className="main-fab" style={{ backgroundColor: 'var(--hcl-primary)' }}><span className="material-icons" style={{ fontSize: '1.875rem', color: 'white', transition: 'transform 0.2s' }}>{isFabMenuOpen ? 'close' : 'add'}</span></button>
                 </div>
            </div>
            <SettingsDrawer isOpen={isDrawerOpen} onClose={toggleDrawer} dialogStyles={dialogStyles} setDialogStyles={setDialogStyles} handleStyleChange={handleStyleChange} handleNumericStyleChange={handleNumericStyleChange} resetSettings={resetSettings} saveSettings={saveSettings} onExport={handleExport}/>
            {isDialogOpen && <DialogComponent styles={dialogStyles} onClose={() => setIsDialogOpen(false)} />}
            <CodeExportModal isOpen={isExportModalOpen} onClose={() => setExportModalOpen(false)} componentName="ConfiguredDialog" jsxCode={generatedCode.jsx} cssCode={generatedCode.css} />
        </div>
    );
}

export default ComplexDialog;