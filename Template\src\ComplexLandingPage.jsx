import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import J<PERSON><PERSON><PERSON> from 'jszip';
import { saveAs } from 'file-saver';
import './ComplexLandingPageIndex.css';

// --- Code Generation Utilities for Complex Landing Page Layout ---

const generateComplexPageLayoutJsx = (styles, viewContainerStyle, displayMode, numCards) => {
  const cardComponent = `
const ConfiguredCard = () => (
  <div className="configured-card">
    {${styles.showHeader} && <div className="card-header-content">${styles.headerContent}</div>}
    <div className="card-body-content">
      <div className="card-actions-placeholder">
        <div className="action-button"></div><div className="action-button"></div><div className="action-button"></div>
      </div>
      <div className="card-content-placeholder">
        <div className="line"></div><div className="line short"></div>
      </div>
    </div>
    {${styles.showFooter} && <div className="card-footer-content">${styles.footerContent}</div>}
  </div>
);`;

  return `
import React from 'react';
import './ConfiguredLayout.css';

// This is a self-contained card component used by the layout.
${cardComponent}

// This is the main configured layout component.
const ConfiguredComplexPageLayout = () => {
  const numCards = ${numCards};

  // The general layout styles are in ConfiguredLayout.css.
  // The dynamic grid styles are applied inline below.
  const viewContainerStyle = {
    display: '${viewContainerStyle.display}',
    gridTemplateColumns: \`${viewContainerStyle.gridTemplateColumns || 'none'}\`,
    gap: '${viewContainerStyle.gap}',
    flexDirection: '${viewContainerStyle.flexDirection || 'row'}'
  };

  const renderCards = () => Array.from({ length: numCards }).map((_, i) => <ConfiguredCard key={i} />);

  return (
    <main className="configured-main-content">
      <div className="content-area">
        <div className="technician-grid" style={viewContainerStyle}>
          {renderCards()}
        </div>
      </div>
    </main>
  );
};

export default ConfiguredComplexPageLayout;
`;
};

const generateComplexPageLayoutCss = (styles) => {
  const cardCss = `
/* === Configured Card Styles === */
.configured-card {
  background: ${styles.bgColor};
  color: ${styles.textColor};
  border: ${styles.borderThickness}px solid ${styles.borderColor};
  width: ${styles.cardWidth ? `${styles.cardWidth}px` : 'auto'};
  height: ${styles.cardHeight ? `${styles.cardHeight}px` : 'auto'};
  min-height: ${styles.cardHeight ? 'unset' : '300px'};
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  transition: all 0.2s ease;
}
.configured-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border-color: #00B4D8;
}
.card-header-content {
  text-align: ${styles.headerAlign};
  color: ${styles.headerColor};
  transform: translate(${styles.headerOffsetX}px, ${styles.headerOffsetY}px);
  height: ${styles.headerHeight ? `${styles.headerHeight}px` : 'auto'};
  padding: 0.5rem 1rem;
  border-bottom: 1px solid ${styles.borderColor};
  background-color: rgba(0,0,0,0.02);
}
.card-footer-content {
  text-align: ${styles.footerAlign};
  color: ${styles.footerColor};
  transform: translate(${styles.footerOffsetX}px, ${styles.footerOffsetY}px);
  height: ${styles.footerHeight ? `${styles.footerHeight}px` : 'auto'};
  padding: 0.5rem 1rem;
  border-top: 1px solid ${styles.borderColor};
  margin-top: auto;
  background-color: rgba(0,0,0,0.02);
}
.card-body-content { flex-grow: 1; padding: 1rem; }
.card-actions-placeholder { display: flex; justify-content: flex-end; gap: 0.5rem; margin-bottom: 1rem; }
.action-button { width: 28px; height: 28px; background-color: ${styles.lineColor}; opacity: 0.5; border-radius: 4px; }
.card-content-placeholder .line { height: 10px; background-color: ${styles.lineColor}; border-radius: 4px; margin-bottom: 0.75rem; width: 90%; }
.card-content-placeholder .line.short { width: 60%; }
`;

  const layoutCss = `
/* === Base and Layout Styles === */
body {
  background-color: #f1f5f9;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  margin: 0;
}
.configured-main-content {
  padding: 16px;
  max-width: 100%;
  box-sizing: border-box;
}
.content-area {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 12px;
}
.technician-grid {
  /* Dynamic grid styles are applied via inline styles in the component. */
}
`;
  return layoutCss + '\n' + cardCss;
};

// --- Reusable Code Export Modal ---
const CodeExportModal = ({ isOpen, onClose, componentName, jsxCode, cssCode }) => {
    if (!isOpen) return null;

    const [activeTab, setActiveTab] = useState('jsx');

    const handleCopyToClipboard = (code) => {
        navigator.clipboard.writeText(code).then(() => alert('Code copied to clipboard!'), () => alert('Failed to copy code.'));
    };

    const generateReadmeContent = (name) => {
      const today = new Date().toLocaleDateString('en-CA'); // YYYY-MM-DD
      const folderName = `my-${name.toLowerCase()}-library`;
      
      return `
# ${name}

This page layout was generated by the HCLSoftware AfterMarket Web Templates application on ${today}.

It is a self-contained layout component displaying a configured set of cards based on the "Complex Landing Page" template.

---

## 1. Installation

1.  **Place the Folder**: Move this entire folder (\`${folderName}\`) into your project's source directory. A common location is \`src/pages/\` or \`src/components/\`.

2.  **Check Dependencies**: This component requires \`react\` and \`react-dom\` to be present in your main project, which is standard for any React application. No other external libraries are needed for this component to function.

---

## 2. Usage

You can use this component directly as a page in your application, for example, within a routing system.

Here is a simple usage example in a parent component like \`App.js\`:

\`\`\`javascript
import React from 'react';
import ${name} from './pages/${folderName}'; // Adjust the import path as needed

function App() {
  return (
    <div>
      {/* You can render the layout directly */}
      <${name} />
    </div>
  );
}

export default App;
\`\`\`

---

## 3. Publishing to NPM (Optional)

If you wish to share this component as a public or private NPM package, you can publish it directly from this folder.

1.  **Login to NPM**: Open your terminal *inside this folder* and run \`npm login\`.
2.  **Publish**: Once logged in, run the command \`npm publish\`.

The \`package.json\` file is already configured with basic information. You may want to edit it to add your own author details, repository link, etc., before publishing.
`;
    };

    const handleDownloadZip = () => {
        const zip = new JSZip();
        const folderName = `my-${componentName.toLowerCase()}-library`;
        const folder = zip.folder(folderName);
        const readmeContent = generateReadmeContent(componentName);

        folder.file(`ConfiguredLayout.jsx`, jsxCode);
        folder.file(`ConfiguredLayout.css`, cssCode);
        folder.file('index.js', `import ${componentName} from './ConfiguredLayout';\nexport default ${componentName};`);
        folder.file('package.json', JSON.stringify({
            name: folderName,
            version: '1.0.0',
            description: `A configurable ${componentName} component generated from the template builder.`,
            main: 'index.js',
            peerDependencies: { "react": ">=16.8.0", "react-dom": ">=16.8.0" },
            license: "ISC"
        }, null, 2));
        folder.file('README.md', readmeContent);

        zip.generateAsync({ type: 'blob' }).then((content) => {
            saveAs(content, `${folderName}.zip`);
        });
    };

    return (
        <div className="export-modal-overlay" onClick={onClose}>
            <div className="export-modal-content" onClick={e => e.stopPropagation()}>
                <div className="export-modal-header">
                    <h3>Export: {componentName}</h3>
                    <button className="close-btn" onClick={onClose}><span className="material-icons">close</span></button>
                </div>
                <div className="export-modal-body">
                    <div className="tab-buttons">
                        <button className={activeTab === 'jsx' ? 'active' : ''} onClick={() => setActiveTab('jsx')}>React Component (JSX)</button>
                        <button className={activeTab === 'css' ? 'active' : ''} onClick={() => setActiveTab('css')}>CSS</button>
                    </div>
                    <div className="code-display-container">
                        <pre><code>{activeTab === 'jsx' ? jsxCode : cssCode}</code></pre>
                        <button className="copy-btn" onClick={() => handleCopyToClipboard(activeTab === 'jsx' ? jsxCode : cssCode)}>Copy</button>
                    </div>
                </div>
                <div className="export-modal-footer">
                    <button className="btn btn-secondary" onClick={onClose}>Close</button>
                    <button className="btn btn-primary" onClick={handleDownloadZip}>Download as ZIP</button>
                </div>
            </div>
        </div>
    );
};


const defaultCardStyles = {
    cardsPerRow: null, cardWidth: null, cardHeight: null, bgColor: '#ffffff',
    textColor: '#1e293b', borderThickness: 1, borderColor: '#e2e8f0',
    lineColor: '#E2E8F0', showHeader: false, headerContent: 'Card Header',
    headerAlign: 'center', headerColor: '#1e293b', headerOffsetX: 0,
    headerOffsetY: 0, headerHeight: null, headerWidth: null, showFooter: false,
    footerContent: 'Card Footer', footerAlign: 'center', footerColor: '#1e293b',
    footerOffsetX: 0, footerOffsetY: 0, footerHeight: null, footerWidth: null
};

const WireframeSidebar = () => <nav className="wireframe-sidebar"></nav>;

const SettingsDrawer = ({ isOpen, onClose, cardStyles, handleStyleChange, handleNumericStyleChange, resetSettings, saveSettings, handleDragStart, previewHeaderRef, previewFooterRef, dragging, activeDraggable, onExport }) => {
    return (
        <div className={`settings-drawer ${isOpen ? 'open' : ''}`}>
            <div className="drawer-content-wrapper">
                <div className="drawer-header">
                    <h3>Card Layout & Style Settings</h3>
                    <button className="close-btn" onClick={onClose} aria-label="Close settings"><span className="material-icons">close</span></button>
                </div>
                <div className="settings-panel">
                    <div className="settings-section">
                        <h4 className="settings-section-title">General Card Properties</h4>
                        <div className="settings-grid">
                            <div className="settings-control"><label htmlFor="cardsPerRow">Cards Per Row:</label><input type="number" id="cardsPerRow" placeholder="Auto" min="1" max="10" value={cardStyles.cardsPerRow || ''} onChange={handleNumericStyleChange}/></div>
                            <div className="settings-control"><label htmlFor="cardWidth">Card Width (px):</label><input type="number" id="cardWidth" placeholder="Auto" min="50" value={cardStyles.cardWidth || ''} onChange={handleNumericStyleChange}/></div>
                            <div className="settings-control"><label htmlFor="cardHeight">Card Height (px):</label><input type="number" id="cardHeight" placeholder="Auto" min="50" value={cardStyles.cardHeight || ''} onChange={handleNumericStyleChange}/></div>
                            <div className="settings-control"><label htmlFor="bgColor">Card BG Color:</label><input type="color" id="bgColor" value={cardStyles.bgColor} onChange={handleStyleChange} /></div>
                            <div className="settings-control"><label htmlFor="textColor">Card Text Color:</label><input type="color" id="textColor" value={cardStyles.textColor} onChange={handleStyleChange} /></div>
                            <div className="settings-control"><label htmlFor="borderThickness">Border (px):</label><input type="number" id="borderThickness" value={cardStyles.borderThickness} onChange={handleNumericStyleChange} min="0" max="10" /></div>
                            <div className="settings-control"><label htmlFor="borderColor">Border Color:</label><input type="color" id="borderColor" value={cardStyles.borderColor} onChange={handleStyleChange} /></div>
                            <div className="settings-control"><label htmlFor="lineColor">Line Color:</label><input type="color" id="lineColor" value={cardStyles.lineColor} onChange={handleStyleChange} /></div>
                        </div>
                    </div>
                    <div className="settings-section">
                       <div className="settings-section-header"><h4 className="settings-section-title">Header Properties</h4><div className="settings-control checkbox-control"><input type="checkbox" id="showHeader" checked={cardStyles.showHeader} onChange={handleStyleChange}/><label htmlFor="showHeader">Show Header</label></div></div>
                        <div className="settings-grid">
                            <div className="settings-control full-width"><label htmlFor="headerContent">Header Text:</label><input type="text" id="headerContent" placeholder="Card Header" value={cardStyles.headerContent} onChange={handleStyleChange} disabled={!cardStyles.showHeader}/></div>
                            <div className="settings-control"><label htmlFor="headerHeight">Height (px):</label><input type="number" id="headerHeight" placeholder="Auto" min="0" value={cardStyles.headerHeight || ''} onChange={handleNumericStyleChange} disabled={!cardStyles.showHeader}/></div>
                            <div className="settings-control"><label htmlFor="headerWidth">Width (px):</label><input type="number" id="headerWidth" placeholder="Auto" min="0" value={cardStyles.headerWidth || ''} onChange={handleNumericStyleChange} disabled={!cardStyles.showHeader}/></div>
                            <div className="settings-control"><label htmlFor="headerAlign">Align:</label><select id="headerAlign" value={cardStyles.headerAlign} onChange={handleStyleChange} disabled={!cardStyles.showHeader}><option value="left">Left</option><option value="center">Center</option><option value="right">Right</option></select></div>
                            <div className="settings-control"><label htmlFor="headerColor">Color:</label><input type="color" id="headerColor" value={cardStyles.headerColor} onChange={handleStyleChange} disabled={!cardStyles.showHeader}/></div>
                            <div className="settings-control"><label htmlFor="headerOffsetX">X Offset (px):</label><input type="number" id="headerOffsetX" value={cardStyles.headerOffsetX} onChange={handleNumericStyleChange} disabled={!cardStyles.showHeader}/></div>
                            <div className="settings-control"><label htmlFor="headerOffsetY">Y Offset (px):</label><input type="number" id="headerOffsetY" value={cardStyles.headerOffsetY} onChange={handleNumericStyleChange} disabled={!cardStyles.showHeader}/></div>
                        </div>
                    </div>
                    <div className="settings-section">
                       <div className="settings-section-header"><h4 className="settings-section-title">Footer Properties</h4><div className="settings-control checkbox-control"><input type="checkbox" id="showFooter" checked={cardStyles.showFooter} onChange={handleStyleChange}/><label htmlFor="showFooter">Show Footer</label></div></div>
                        <div className="settings-grid">
                            <div className="settings-control full-width"><label htmlFor="footerContent">Footer Text:</label><input type="text" id="footerContent" placeholder="Card Footer" value={cardStyles.footerContent} onChange={handleStyleChange} disabled={!cardStyles.showFooter}/></div>
                            <div className="settings-control"><label htmlFor="footerHeight">Height (px):</label><input type="number" id="footerHeight" placeholder="Auto" min="0" value={cardStyles.footerHeight || ''} onChange={handleNumericStyleChange} disabled={!cardStyles.showFooter}/></div>
                            <div className="settings-control"><label htmlFor="footerWidth">Width (px):</label><input type="number" id="footerWidth" placeholder="Auto" min="0" value={cardStyles.footerWidth || ''} onChange={handleNumericStyleChange} disabled={!cardStyles.showFooter}/></div>
                            <div className="settings-control"><label htmlFor="footerAlign">Align:</label><select id="footerAlign" value={cardStyles.footerAlign} onChange={handleStyleChange} disabled={!cardStyles.showFooter}><option value="left">Left</option><option value="center">Center</option><option value="right">Right</option></select></div>
                            <div className="settings-control"><label htmlFor="footerColor">Color:</label><input type="color" id="footerColor" value={cardStyles.footerColor} onChange={handleStyleChange} disabled={!cardStyles.showFooter}/></div>
                            <div className="settings-control"><label htmlFor="footerOffsetX">X Offset (px):</label><input type="number" id="footerOffsetX" value={cardStyles.footerOffsetX} onChange={handleNumericStyleChange} disabled={!cardStyles.showFooter}/></div>
                            <div className="settings-control"><label htmlFor="footerOffsetY">Y Offset (px):</label><input type="number" id="footerOffsetY" value={cardStyles.footerOffsetY} onChange={handleNumericStyleChange} disabled={!cardStyles.showFooter}/></div>
                        </div>
                    </div>
                </div>
                <div className="preview-card-container">
                    <div id="previewCard" className="preview-card" style={{ backgroundColor: cardStyles.bgColor, color: cardStyles.textColor, border: `${cardStyles.borderThickness}px solid ${cardStyles.borderColor}`, width: cardStyles.cardWidth ? `${cardStyles.cardWidth}px` : '250px', height: cardStyles.cardHeight ? `${cardStyles.cardHeight}px` : '250px' }}>
                        {cardStyles.showHeader && (<div ref={previewHeaderRef} id="previewHeader" className={`preview-header-content ${dragging && activeDraggable === 'header' ? 'draggable' : ''}`} style={{ textAlign: cardStyles.headerAlign, color: cardStyles.headerColor, transform: `translate(${cardStyles.headerOffsetX}px, ${cardStyles.headerOffsetY}px)`, height: cardStyles.headerHeight ? `${cardStyles.headerHeight}px` : undefined, width: cardStyles.headerWidth ? `${cardStyles.headerWidth}px` : undefined }} onMouseDown={(e) => cardStyles.showHeader && handleDragStart(e, 'header')} onTouchStart={(e) => cardStyles.showHeader && handleDragStart(e, 'header')}>{cardStyles.headerContent || 'Header'}</div>)}
                        <div className="preview-card-content"><div className="line" style={{ backgroundColor: cardStyles.lineColor }}></div><div className="line" style={{ backgroundColor: cardStyles.lineColor, width: '90%', marginTop: '4px' }}></div><div className="line" style={{ backgroundColor: cardStyles.lineColor, width: '60%', marginTop: '4px' }}></div></div>
                        {cardStyles.showFooter && (<div ref={previewFooterRef} id="previewFooter" className={`preview-footer-content ${dragging && activeDraggable === 'footer' ? 'draggable' : ''}`} style={{ textAlign: cardStyles.footerAlign, color: cardStyles.footerColor, transform: `translate(${cardStyles.footerOffsetX}px, ${cardStyles.footerOffsetY}px)`, height: cardStyles.footerHeight ? `${cardStyles.footerHeight}px` : undefined, width: cardStyles.footerWidth ? `${cardStyles.footerWidth}px` : undefined }} onMouseDown={(e) => cardStyles.showFooter && handleDragStart(e, 'footer')} onTouchStart={(e) => cardStyles.showFooter && handleDragStart(e, 'footer')}>{cardStyles.footerContent || 'Footer'}</div>)}
                    </div>
                </div>
                <div className="drawer-actions">
                    <button className="btn btn-export" onClick={onExport}><span className="material-icons">code</span>Export</button>
                    <button className="btn btn-secondary" onClick={resetSettings}><span className="material-icons">restart_alt</span>Reset</button>
                    <button className="btn btn-primary" onClick={saveSettings}><span className="material-icons">save</span>Save</button>
                </div>
            </div>
        </div>
    );
};

function ComplexLandingPage() {
    const navigate = useNavigate();
    const [displayMode, setDisplayMode] = useState('cards');
    const [numCards, setNumCards] = useState(10);
    const [cardStyles, setCardStyles] = useState({ ...defaultCardStyles });
    const [isDrawerOpen, setIsDrawerOpen] = useState(false);
    const [windowWidth, setWindowWidth] = useState(window.innerWidth);
    const [dragging, setDragging] = useState(false);
    const [activeDraggable, setActiveDraggable] = useState(null);
    const dragStartPos = useRef({ x: 0, y: 0 });
    const elementStartOffset = useRef({ x: 0, y: 0 });
    const previewHeaderRef = useRef(null);
    const previewFooterRef = useRef(null);

    const [isExportModalOpen, setExportModalOpen] = useState(false);
    const [generatedCode, setGeneratedCode] = useState({ jsx: '', css: '' });

    const getCalculatedColumns = useCallback((mode, cards, width) => {
        let maxCols;
        if (mode === 'cards') maxCols = width >= 992 ? 3 : width >= 768 ? 2 : 1;
        else if (mode === 'compact' || mode === 'normal-grid') maxCols = width >= 992 ? 5 : width >= 768 ? 3 : 2;
        else return 1;
        return Math.min(cards, maxCols);
    }, []);

    const viewContainerStyle = useMemo(() => {
        if (displayMode === 'list' || displayMode === 'normal-grid') return { display: 'flex', flexDirection: 'column', gap: 'var(--spacing-md)' };
        if (displayMode === 'cards' || displayMode === 'compact') {
            const columns = cardStyles.cardsPerRow || getCalculatedColumns(displayMode, numCards, windowWidth);
            return { display: 'grid', gridTemplateColumns: `repeat(${columns}, 1fr)`, gap: 'var(--spacing-md)' };
        }
        return {};
    }, [displayMode, cardStyles.cardsPerRow, numCards, windowWidth, getCalculatedColumns]);

    const handleExport = () => {
        const jsx = generateComplexPageLayoutJsx(cardStyles, viewContainerStyle, displayMode, numCards);
        const css = generateComplexPageLayoutCss(cardStyles);
        setGeneratedCode({ jsx, css });
        setExportModalOpen(true);
    };
    
    const handleGoBack = () => navigate(-1);
    const handleStyleChange = (e) => {
        const { id, value, type, checked } = e.target;
        setCardStyles(prev => ({ ...prev, [id]: type === 'checkbox' ? checked : value }));
    };
    const handleNumericStyleChange = (e) => {
        const { id, value } = e.target;
        setCardStyles(prev => ({ ...prev, [id]: value === '' ? null : parseInt(value, 10) }));
    };
    const toggleDrawer = () => setIsDrawerOpen(!isDrawerOpen);
    const resetSettings = () => setCardStyles({ ...defaultCardStyles });
    const saveSettings = () => toggleDrawer();
    const getTranslateXY = (elm) => {
        if (!elm) return { x: 0, y: 0 };
        const matrix = new DOMMatrixReadOnly(window.getComputedStyle(elm).transform);
        return { x: matrix.m41, y: matrix.m42 };
    };
    const handleDragStart = useCallback((e, elementId) => {
        e.preventDefault(); setDragging(true); setActiveDraggable(elementId);
        const targetElement = elementId === 'header' ? previewHeaderRef.current : previewFooterRef.current;
        elementStartOffset.current = getTranslateXY(targetElement);
        dragStartPos.current = { x: e.clientX || e.touches[0].clientX, y: e.clientY || e.touches[0].clientY };
        document.addEventListener('mousemove', handleDrag); document.addEventListener('mouseup', handleDragEnd);
        document.addEventListener('touchmove', handleDrag); document.addEventListener('touchend', handleDragEnd);
    }, []);
    const handleDrag = useCallback((e) => {
        if (!dragging) return;
        const clientX = e.clientX || e.touches[0].clientX; const clientY = e.clientY || e.touches[0].clientY;
        const dx = clientX - dragStartPos.current.x; const dy = clientY - dragStartPos.current.y;
        const newX = elementStartOffset.current.x + dx; const newY = elementStartOffset.current.y + dy;
        const targetElement = activeDraggable === 'header' ? previewHeaderRef.current : previewFooterRef.current;
        if (targetElement) targetElement.style.transform = `translate(${newX}px, ${newY}px)`;
        if (activeDraggable === 'header') setCardStyles(prev => ({ ...prev, headerOffsetX: newX, headerOffsetY: newY }));
        else if (activeDraggable === 'footer') setCardStyles(prev => ({ ...prev, footerOffsetX: newX, footerOffsetY: newY }));
    }, [dragging, activeDraggable]);
    const handleDragEnd = useCallback(() => {
        setDragging(false); setActiveDraggable(null);
        document.removeEventListener('mousemove', handleDrag); document.removeEventListener('mouseup', handleDragEnd);
        document.removeEventListener('touchmove', handleDrag); document.removeEventListener('touchend', handleDragEnd);
    }, [handleDrag]);

    const EmptyCard = ({ mode, styles }) => {
        const cardStyle = {
            background: styles.bgColor, color: styles.textColor, border: `${styles.borderThickness}px solid ${styles.borderColor}`,
            width: styles.cardWidth ? `${styles.cardWidth}px` : undefined, minHeight: styles.cardHeight ? `${styles.cardHeight}px` : (mode === 'cards' ? '300px' : (mode === 'normal-grid' ? '70px' : '100px')),
            height: styles.cardHeight ? `${styles.cardHeight}px` : undefined,
        };
        const lineStyle = { backgroundColor: styles.lineColor };
        const headerStyle = {
            textAlign: styles.headerAlign, color: styles.headerColor, transform: `translate(${styles.headerOffsetX}px, ${styles.headerOffsetY}px)`,
            height: styles.headerHeight ? `${styles.headerHeight}px` : undefined, width: styles.headerWidth ? `${styles.headerWidth}px` : undefined,
        };
        const footerStyle = {
            textAlign: styles.footerAlign, color: styles.footerColor, transform: `translate(${styles.footerOffsetX}px, ${styles.footerOffsetY}px)`,
            height: styles.footerHeight ? `${styles.footerHeight}px` : undefined, width: styles.footerWidth ? `${styles.footerWidth}px` : undefined,
        };
        const cardActionsHTML = <div className="card-header"><div className="card-actions"><button className="action-btn" title="View"><span className="material-icons">visibility</span></button><button className="action-btn" title="Edit"><span className="material-icons">edit</span></button><button className="action-btn" title="Delete"><span className="material-icons">delete</span></button></div></div>;
        const contentPlaceholderHTML = <div className="card-content-placeholder" style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', padding: '1rem', flexGrow: 1 }}><div className="line" style={{ ...lineStyle, width: '80%' }}></div><div className="line" style={{ ...lineStyle, width: '95%', marginTop: '0.25rem' }}></div><div className="line" style={{ ...lineStyle, width: '60%', marginTop: '0.25rem' }}></div></div>;
        const mainCardContentHTML = (mode === 'cards' || mode === 'compact') ? (<>{styles.showHeader && <div className="card-header-content" style={headerStyle}>{styles.headerContent || 'Header'}</div>}{cardActionsHTML}{contentPlaceholderHTML}{styles.showFooter && <div className="card-footer-content" style={footerStyle}>{styles.footerContent || 'Footer'}</div>}</>) : (<>{styles.showHeader && <div className="card-header-content" style={headerStyle}>{styles.headerContent || 'Header'}</div>}{contentPlaceholderHTML}{styles.showFooter && <div className="card-footer-content" style={footerStyle}>{styles.footerContent || 'Footer'}</div>}{cardActionsHTML}</>);
        return <div className="technician-card" style={cardStyle}>{mainCardContentHTML}</div>;
    };

    useEffect(() => {
        const handleResize = () => setWindowWidth(window.innerWidth);
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const renderCards = () => Array.from({ length: numCards }, (_, i) => <EmptyCard key={i} mode={displayMode} styles={cardStyles} />);
    useEffect(() => { document.body.className = 'light-theme'; }, []);

    return (
        <div className="app-container">
            <div className={`app-body ${isDrawerOpen ? 'settings-drawer-is-open' : ''}`}>
                <header className="app-header">
                    <div className="header-left">
                        <button className="back-button" aria-label="Go back" onClick={handleGoBack}><span className="material-icons">arrow_back</span></button>
                        <h1 className="page-title">Menu/Navigation</h1>
                    </div>
                    <div className="header-right">
                        <button className="btn btn-secondary" onClick={toggleDrawer}><span className="material-icons">settings</span>Settings</button>
                        <button className="btn btn-primary"><span className="material-icons">add</span>Add New</button>
                    </div>
                </header>
                <div className="main-content-area">
                    <WireframeSidebar />
                    <div className="content-scroll-wrapper">
                        <main className="main-content">
                            <div className="stats-grid"><div className="stats-section"></div><div className="actions-section"></div></div>
                            <div className="controls-section">
                                <div className="search-filters">
                                    <div className="status-filter-group"></div><div className="flex items-center"></div>
                                    <div className="view-toggle">
                                        <button className={`view-btn ${displayMode === 'cards' ? 'active' : ''}`} onClick={() => setDisplayMode('cards')}><span className="material-icons">view_module</span></button>
                                        <button className={`view-btn ${displayMode === 'list' ? 'active' : ''}`} onClick={() => setDisplayMode('list')}><span className="material-icons">view_list</span></button>
                                        <button className={`view-btn ${displayMode === 'compact' ? 'active' : ''}`} onClick={() => setDisplayMode('compact')}><span className="material-icons">grid_on</span></button>
                                        <button className={`view-btn ${displayMode === 'normal-grid' ? 'active' : ''}`} onClick={() => setDisplayMode('normal-grid')}><span className="material-icons">apps</span></button>
                                    </div>
                                </div>
                            </div>
                            <div className="content-area">
                                <div id="cards-view" className="technician-grid cards-view" style={{ ...viewContainerStyle, display: displayMode === 'cards' ? viewContainerStyle.display : 'none' }}>{displayMode === 'cards' && renderCards()}</div>
                                <div id="list-view" className="technician-grid list-view" style={{ ...viewContainerStyle, display: displayMode === 'list' ? viewContainerStyle.display : 'none' }}>{displayMode === 'list' && renderCards()}</div>
                                <div id="compact-view" className="technician-grid compact-view" style={{ ...viewContainerStyle, display: displayMode === 'compact' ? viewContainerStyle.display : 'none' }}>{displayMode === 'compact' && renderCards()}</div>
                                <div id="normal-grid-view" className="technician-grid normal-grid-view" style={{ ...viewContainerStyle, display: displayMode === 'normal-grid' ? viewContainerStyle.display : 'none' }}>{displayMode === 'normal-grid' && renderCards()}</div>
                            </div>
                        </main>
                    </div>
                </div>
                 <div className="fab-container">
                    <button id="mainFab" className="main-fab" style={{ backgroundColor: 'var(--hcl-primary)' }}><span className="material-icons" style={{ fontSize: '1.875rem', color: 'white' }}>add</span></button>
                    <div id="flyoutActions" className="flyout-actions-group"><button className="flyout-action-btn" title="Add Technician"><span className="material-icons">person_add</span></button><button className="flyout-action-btn" title="Upload Data"><span className="material-icons">upload_file</span></button><button className="flyout-action-btn" title="Generate Report"><span className="material-icons">receipt_long</span></button></div>
                </div>
            </div>
            <SettingsDrawer isOpen={isDrawerOpen} onClose={toggleDrawer} cardStyles={cardStyles} handleStyleChange={handleStyleChange} handleNumericStyleChange={handleNumericStyleChange} resetSettings={resetSettings} saveSettings={saveSettings} handleDragStart={handleDragStart} previewHeaderRef={previewHeaderRef} previewFooterRef={previewFooterRef} dragging={dragging} activeDraggable={activeDraggable} onExport={handleExport} />
            <CodeExportModal isOpen={isExportModalOpen} onClose={() => setExportModalOpen(false)} componentName="ConfiguredComplexPageLayout" jsxCode={generatedCode.jsx} cssCode={generatedCode.css} />
        </div>
    );
}

export default ComplexLandingPage;