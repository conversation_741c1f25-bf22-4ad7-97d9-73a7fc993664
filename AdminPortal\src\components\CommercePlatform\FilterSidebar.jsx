import React from 'react';
// import './FilterSidebar.css';

const AdvancedFilter = ({
  search,
  setSearch,
  tags,
  selectedTags,
  setSelectedTags,
  features,
  selectedFeatures,
  setSelectedFeatures,
  industries,
  selectedIndustries,
  setSelectedIndustries,
}) => {
  return (
    <div className="advanced-filter-ui">
      <div className="filter-group-row">
        <div className="filter-group-col filter-search-col">
          <div className="filter-group-label">Search</div>
          <input
            type="text"
            placeholder="Search Solutions..."
            value={search}
            onChange={e => setSearch(e.target.value)}
            className="filter-search-input"
          />
        </div>
        <div className="filter-group-col">
          <div className="filter-group-label">Tags</div>
          <div className="filter-pills-wrap">
            {tags.map(tag => (
              <label key={tag} className="filter-pill">
                <input
                  type="checkbox"
                  checked={selectedTags.includes(tag)}
                  onChange={() => setSelectedTags(selectedTags.includes(tag)
                    ? selectedTags.filter(t => t !== tag)
                    : [...selectedTags, tag])}
                />
                {tag}
              </label>
            ))}
          </div>
        </div>
        <div className="filter-group-col">
          <div className="filter-group-label">Features</div>
          <div className="filter-pills-wrap">
            {features.map(feature => (
              <label key={feature} className="filter-pill">
                <input
                  type="checkbox"
                  checked={selectedFeatures.includes(feature)}
                  onChange={() => setSelectedFeatures(selectedFeatures.includes(feature)
                    ? selectedFeatures.filter(f => f !== feature)
                    : [...selectedFeatures, feature])}
                />
                {feature}
              </label>
            ))}
          </div>
        </div>
        <div className="filter-group-col">
          <div className="filter-group-label">Industries</div>
          <div className="filter-pills-wrap">
            {industries.map(ind => (
              <label key={ind} className="filter-pill">
                <input
                  type="checkbox"
                  checked={selectedIndustries.includes(ind)}
                  onChange={() => setSelectedIndustries(selectedIndustries.includes(ind)
                    ? selectedIndustries.filter(i => i !== ind)
                    : [...selectedIndustries, ind])}
                />
                {ind}
              </label>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedFilter; 