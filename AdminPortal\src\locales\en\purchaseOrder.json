{"title": "Purchase Order Management", "buttons": {"add": "Add Purchase Order", "edit": "Edit", "delete": "Delete", "view": "View Details", "save": "Save", "cancel": "Cancel", "export": "Export", "import": "Import", "search": "Search", "filter": "Filter", "reset": "Reset", "apply": "Apply", "close": "Close"}, "labels": {"companyInfo": "Company Information", "owner": "Owner", "parentCompany": "Parent Company Name", "accountName": "Account Name", "domain": "Domain/Vertical", "region": "Region", "financialDetails": "Financial Details", "revenueType": "Revenue Type", "poNumber": "PO #", "poCurrency": "PO Currency", "poValue": "PO Value", "conversionRate": "Conversion Rate of Currency", "hclConversionRate": "HCL Conversion Rate of USD", "timelineInfo": "Timeline Information", "opportunityStage": "Opportunity Stage", "frequency": "Frequency of Realization", "startDate": "Start Date", "endDate": "End Date", "productDetails": "Product Details", "renewalUpsell": "Renewal/Upsell", "productCategory": "Product Revenue Category", "productName": "Product Name", "projectDescription": "Project/Description", "calculatedFields": "Calculated Fields", "poFunnelValueINR": "PO/Funnel Value in INR", "usdEquivalent": "USD Equivalent", "grandTotalINR": "Grand Total (INR)", "grandTotalUSD": "Grand Total (USD)", "fy2025Total": "FY 2025-26 Total"}, "months": {"apr25": "Apr'25", "may25": "May'25", "jun25": "Jun'25", "jul25": "Jul'25", "aug25": "Aug'25", "sep25": "Sep'25", "oct25": "Oct'25", "nov25": "Nov'25", "dec25": "Dec'25", "jan26": "Jan'26", "feb26": "Feb'26", "mar26": "Mar'26"}, "quarters": {"amj25": "AMJ'25", "jas25": "JAS'25", "ond25": "OND'25", "jfm26": "JFM'26"}, "tracking": {"projected": "Projected", "actual": "Actual", "monthly": "Monthly Tracking", "quarterly": "Quarterly Summary"}, "status": {"draft": "Draft", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "completed": "Completed", "cancelled": "Cancelled"}, "validation": {"required": "This field is required", "invalidEmail": "Please enter a valid email address", "invalidNumber": "Please enter a valid number", "invalidDate": "Please enter a valid date", "minValue": "Value must be greater than 0", "maxLength": "Maximum length exceeded"}, "messages": {"saveSuccess": "Purchase Order saved successfully", "deleteSuccess": "Purchase Order deleted successfully", "exportSuccess": "Data exported successfully", "loadingData": "Loading purchase orders...", "noData": "No purchase orders found", "confirmDelete": "Are you sure you want to delete this purchase order?", "unsavedChanges": "You have unsaved changes. Do you want to continue?"}}