import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FiHome, FiUsers, FiFileText, FiDollarSign, FiShield, FiShoppingCart,
  FiAward, FiHeadphones, FiBriefcase, FiFile, FiTruck, FiServer, FiDatabase,
  FiTrendingUp, FiTrendingDown, FiActivity, FiBarChart2, FiPieChart,
  FiCalendar, FiClock, FiAlertCircle, FiCheckCircle, FiXCircle
} from 'react-icons/fi';
import {
  Box, Grid, Typography, Button,
  LinearProgress, IconButton, Tooltip
} from '@mui/material';
import { AdminComponents, theme } from '../../../styles/theme';
import Chart from 'chart.js/auto';

const HclDashboard = () => {
  const navigate = useNavigate();
  const [dashboardData, setDashboardData] = useState({
    totalCustomers: 1247,
    activeContracts: 89,
    monthlyRevenue: 2450000,
    supportTickets: 23,
    systemHealth: 98.5,
    pendingApprovals: 12
  });

  // Chart state and refs
  const [chartType, setChartType] = useState('bar');
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  const [recentActivities] = useState([
    { id: 1, type: 'contract', message: 'New contract signed with TechCorp', time: '2 hours ago', status: 'success' },
    { id: 2, type: 'support', message: 'Critical support ticket resolved', time: '4 hours ago', status: 'info' },
    { id: 3, type: 'customer', message: 'New customer onboarded', time: '6 hours ago', status: 'success' },
    { id: 4, type: 'system', message: 'System maintenance completed', time: '1 day ago', status: 'warning' },
    { id: 5, type: 'invoice', message: 'Monthly invoices generated', time: '2 days ago', status: 'info' }
  ]);

  const [quickStats] = useState([
    { title: 'Revenue Growth', value: '+12.5%', trend: 'up', color: 'success' },
    { title: 'Customer Satisfaction', value: '94.2%', trend: 'up', color: 'success' },
    { title: 'System Uptime', value: '99.9%', trend: 'stable', color: 'info' },
    { title: 'Response Time', value: '2.3s', trend: 'down', color: 'warning' }
  ]);

  const summaryCards = [
    {
      title: 'Total Customers',
      value: dashboardData.totalCustomers,
      icon: <FiUsers />,
      color: 'primary',
      trend: '+8.2%',
      trendDirection: 'up',
      path: '/admin/onboarding'
    },
    {
      title: 'Active Contracts',
      value: dashboardData.activeContracts,
      icon: <FiFileText />,
      color: 'success',
      trend: '****%',
      trendDirection: 'up',
      path: '/admin/contract'
    },
    {
      title: 'Monthly Revenue',
      value: `$${(dashboardData.monthlyRevenue / 1000000).toFixed(1)}M`,
      icon: <FiDollarSign />,
      color: 'info',
      trend: '+12.5%',
      trendDirection: 'up',
      path: '/admin/revenue'
    },
    {
      title: 'Support Tickets',
      value: dashboardData.supportTickets,
      icon: <FiHeadphones />,
      color: 'warning',
      trend: '-3.2%',
      trendDirection: 'down',
      path: '/admin/support'
    }
  ];

  const quickActions = [
    { title: 'Add Customer', icon: <FiUsers />, path: '/admin/OnBoarding', color: 'primary' },
    { title: 'New Contract', icon: <FiFileText />, path: '/admin/contract', color: 'success' },
    { title: 'Generate Invoice', icon: <FiDollarSign />, path: '/admin/invoices', color: 'info' },
    { title: 'System Health', icon: <FiActivity />, path: '/admin/system', color: 'warning' }
  ];

  const getActivityIcon = (type) => {
    switch (type) {
      case 'contract': return <FiFileText />;
      case 'support': return <FiHeadphones />;
      case 'customer': return <FiUsers />;
      case 'system': return <FiServer />;
      case 'invoice': return <FiDollarSign />;
      default: return <FiActivity />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'success';
      case 'warning': return 'warning';
      case 'error': return 'error';
      default: return 'info';
    }
  };

  const getThemeColor = (variant) => {
    switch (variant) {
      case 'primary': return theme.palette.primary.main;
      case 'success': return theme.palette.success.main;
      case 'warning': return theme.palette.warning.main;
      case 'error': return theme.palette.error.main;
      case 'info': return theme.palette.info.main;
      default: return theme.palette.info.main;
    }
  };

  // Chart effect
  useEffect(() => {
    if (chartInstance.current) {
      chartInstance.current.destroy();
    }
    if (chartRef.current) {
      const ctx = chartRef.current.getContext('2d');
      chartInstance.current = new Chart(ctx, {
        type: chartType,
        data: {
          labels: ['Revenue Growth', 'Customer Satisfaction', 'System Uptime', 'Response Time'],
          datasets: [{
            label: 'Performance Metrics',
            data: [12.5, 94.2, 99.9, 2.3],
            backgroundColor: chartType === 'pie' ? theme.palette.chart.backgrounds : theme.palette.primary.main,
            borderColor: theme.palette.primary.dark,
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: true,
              text: 'Performance Overview'
            },
            legend: {
              display: chartType === 'pie'
            }
          },
          scales: chartType !== 'pie' ? {
            y: {
              beginAtZero: true
            }
          } : {}
        }
      });
    }
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [chartType]);

  return (
    <AdminComponents.AppContainer>
      <AdminComponents.MainContentArea>
        {/* Welcome Section */}
        <AdminComponents.TopSectionWrapper>
          <Box>
            <Typography variant="h4" className="auth-modal-headline">
              Welcome to HCL Admin Dashboard
            </Typography>
          </Box>
        </AdminComponents.TopSectionWrapper>

        {/* Summary Cards - All 7 cards in one row */}
        <AdminComponents.SummaryCardsContainer>
          {summaryCards.map((card, index) => (
            <AdminComponents.SummaryCard key={index} onClick={() => navigate(card.path)}>
              <AdminComponents.SummaryAvatar variant={card.color}>
                {card.icon}
              </AdminComponents.SummaryAvatar>
              <Box>
                <Typography variant="h4" className="auth-modal-headline">
                  {card.value}
                </Typography>
                <Typography variant="body2" className="auth-modal-values">
                  {card.title}
                </Typography>
                <AdminComponents.TopSectionActions>
                  {card.trendDirection === 'up' ? (
                    <FiTrendingUp />
                  ) : (
                    <FiTrendingDown />
                  )}
                  <Typography variant="caption" className="auth-modal-headline">
                    {card.trend}
                  </Typography>
                </AdminComponents.TopSectionActions>
              </Box>
            </AdminComponents.SummaryCard>
          ))}

          {/* Additional Metrics - Same row */}
          <AdminComponents.SummaryCard onClick={() => navigate('/admin/approvals')}>
            <AdminComponents.SummaryAvatar variant="primary">
              <FiCalendar />
            </AdminComponents.SummaryAvatar>
            <Box>
              <Typography variant="h4" className="auth-modal-headline">
                {dashboardData.pendingApprovals}
              </Typography>
              <Typography variant="body2" className="auth-modal-values">
                Pending Approvals
              </Typography>
              <Typography variant="caption" className="auth-modal-values">
                Review Now
              </Typography>
            </Box>
          </AdminComponents.SummaryCard>

          <AdminComponents.SummaryCard onClick={() => navigate('/admin/performance')}>
            <AdminComponents.SummaryAvatar variant="info">
              <FiClock />
            </AdminComponents.SummaryAvatar>
            <Box>
              <Typography variant="h4" className="auth-modal-headline">
                2.3s
              </Typography>
              <Typography variant="body2" className="auth-modal-values">
                Avg Response Time
              </Typography>
              <Typography variant="caption" className="auth-modal-values">
                View Details
              </Typography>
            </Box>
          </AdminComponents.SummaryCard>

          <AdminComponents.SummaryCard onClick={() => navigate('/admin/security')}>
            <AdminComponents.SummaryAvatar variant="success">
              <FiShield />
            </AdminComponents.SummaryAvatar>
            <Box>
              <Typography variant="h4" className="auth-modal-headline">
                99.9%
              </Typography>
              <Typography variant="body2" className="auth-modal-values">
                Security Score
              </Typography>
              <Typography variant="caption" className="auth-modal-values">
                Security Center
              </Typography>
            </Box>
          </AdminComponents.SummaryCard>
        </AdminComponents.SummaryCardsContainer>

        {/* Quick Actions */}
        <AdminComponents.TopSectionWrapper>
          <Typography variant="h6" className="auth-modal-headline">
            Quick Actions
          </Typography>
          <Grid container spacing={3}>
            {quickActions.map((action, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <AdminComponents.CardBase onClick={() => navigate(action.path)}>
                  <AdminComponents.PaddedCardContent>
                    <AdminComponents.SummaryAvatar variant={action.color}>
                      {action.icon}
                    </AdminComponents.SummaryAvatar>
                    <Typography variant="h6" className="auth-modal-headline">
                      {action.title}
                    </Typography>
                  </AdminComponents.PaddedCardContent>
                </AdminComponents.CardBase>
              </Grid>
            ))}
          </Grid>
        </AdminComponents.TopSectionWrapper>

        {/* Main Content Grid - 3 Sections */}
        <Grid container spacing={4}>
          {/* Section 1: Performance Overview (Chart) */}
          <Grid item xs={12} md={6} lg={3}>
            <AdminComponents.MainLeftPane style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
              <AdminComponents.TopSectionContent>
                <Typography variant="h6" className="auth-modal-headline">
                  Performance Overview
                </Typography>
                <AdminComponents.TopSectionActions>
                  <Tooltip title="Bar Chart">
                    <IconButton
                      size="small"
                      onClick={() => setChartType('bar')}
                      color={chartType === 'bar' ? 'primary' : 'default'}
                    >
                      <FiBarChart2 />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Pie Chart">
                    <IconButton
                      size="small"
                      onClick={() => setChartType('pie')}
                      color={chartType === 'pie' ? 'primary' : 'default'}
                    >
                      <FiPieChart />
                    </IconButton>
                  </Tooltip>
                </AdminComponents.TopSectionActions>
              </AdminComponents.TopSectionContent>
              {/* Performance Chart */}
              <Box sx={{ flexGrow: 1, display: 'flex', alignItems: 'center', justifyContent: 'center', minHeight: 250 }}>
                <AdminComponents.GraphWrapper>
                  <AdminComponents.GraphCanvasContainer style={{ height: '260px' }}>
                    <canvas ref={chartRef}></canvas>
                  </AdminComponents.GraphCanvasContainer>
                </AdminComponents.GraphWrapper>
              </Box>
            </AdminComponents.MainLeftPane>
          </Grid>

          {/* Section 2: Performance Overview (Trends/Details) */}
          <Grid item xs={12} md={6} lg={3}>
            <AdminComponents.MainLeftPane>
              <AdminComponents.TopSectionContent>
                <Typography variant="h6" className="auth-modal-headline">
                  Performance Trends
                </Typography>
              </AdminComponents.TopSectionContent>
              <AdminComponents.GridView style={{ gridTemplateColumns: 'repeat(2, 1fr)' }}>
                {quickStats.map((stat, index) => (
                  <AdminComponents.CardBase key={index}>
                    <AdminComponents.PaddedCardContent>
                      <Typography variant="h6" className="auth-modal-headline">
                        {stat.value}
                      </Typography>
                      <Typography variant="caption" className="auth-modal-values">
                        {stat.title}
                      </Typography>
                      <AdminComponents.TopSectionActions>
                        {stat.trend === 'up' && <FiTrendingUp />}
                        {stat.trend === 'down' && <FiTrendingDown />}
                        {stat.trend === 'stable' && <FiActivity />}
                      </AdminComponents.TopSectionActions>
                    </AdminComponents.PaddedCardContent>
                  </AdminComponents.CardBase>
                ))}
              </AdminComponents.GridView>
            </AdminComponents.MainLeftPane>
          </Grid>

          {/* Section 4: System Status */}
          <Grid item xs={12} md={6} lg={2}>
            <AdminComponents.DetailsPane>
              <Typography variant="h6" className="auth-modal-headline">
                System Status
              </Typography>
              <Box>
                <AdminComponents.TopSectionContent>
                  <Typography variant="body2" className="auth-modal-values">
                    Overall Health
                  </Typography>
                  <Typography variant="body2" className="auth-modal-headline">
                    {dashboardData.systemHealth}%
                  </Typography>
                </AdminComponents.TopSectionContent>
                <LinearProgress
                  variant="determinate"
                  value={dashboardData.systemHealth}
                  className="password-strength-bar"
                />
              </Box>
              <AdminComponents.StyledDivider />
              <AdminComponents.SidebarContent>
                <AdminComponents.TopSectionContent>
                  <Box>
                    <Typography variant="body2" className="auth-modal-headline">
                      Database
                    </Typography>
                    <Typography variant="caption" className="auth-modal-values">
                      Running smoothly
                    </Typography>
                  </Box>
                </AdminComponents.TopSectionContent>
                <AdminComponents.TopSectionContent>
                  <Box>
                    <Typography variant="body2" className="auth-modal-headline">
                      API Services
                    </Typography>
                    <Typography variant="caption" className="auth-modal-values">
                      All endpoints active
                    </Typography>
                  </Box>
                </AdminComponents.TopSectionContent>
                <AdminComponents.TopSectionContent>
                  <Box>
                    <Typography variant="body2" className="auth-modal-headline">
                      Storage
                    </Typography>
                    <Typography variant="caption" className="auth-modal-values">
                      85% capacity
                    </Typography>
                  </Box>
                </AdminComponents.TopSectionContent>
                <AdminComponents.TopSectionContent>
                  <Box>
                    <Typography variant="body2" className="auth-modal-headline">
                      Security
                    </Typography>
                    <Typography variant="caption" className="auth-modal-values">
                      No threats detected
                    </Typography>
                  </Box>
                </AdminComponents.TopSectionContent>
              </AdminComponents.SidebarContent>
            </AdminComponents.DetailsPane>
          </Grid>

          {/* Section 5: Recent Activities */}
          <Grid item xs={12} md={12} lg={2}>
            <AdminComponents.ActivityLogPaper style={{ minHeight: 500, display: 'flex', flexDirection: 'column' }}>
              <AdminComponents.TopSectionContent>
                <AdminComponents.ActivityLogTitle variant="h6">
                  Recent Activities
                </AdminComponents.ActivityLogTitle>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => navigate('/admin/activities')}
                >
                  View All
                </Button>
              </AdminComponents.TopSectionContent>
              <AdminComponents.ActivityLogListContainer style={{ flexGrow: 0, maxHeight: 'none', overflow: 'visible' }}>
                {recentActivities.map((activity) => (
                  <AdminComponents.ActivityLogListItem key={activity.id}>
                    <Box>
                      <Typography variant="body2" className="auth-modal-headline">
                        {activity.message}
                      </Typography>
                      <Typography variant="caption" className="auth-modal-values">
                        {activity.time}
                      </Typography>
                    </Box>
                    <AdminComponents.StatusBadge
                      label={activity.status}
                      size="small"
                      ownerState={{ status: activity.status }}
                      variant="outlined"
                    />
                  </AdminComponents.ActivityLogListItem>
                ))}
              </AdminComponents.ActivityLogListContainer>
            </AdminComponents.ActivityLogPaper>
          </Grid>
        </Grid>



      </AdminComponents.MainContentArea>
    </AdminComponents.AppContainer>
  );
};

export default HclDashboard;