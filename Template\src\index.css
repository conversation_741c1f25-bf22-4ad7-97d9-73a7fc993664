/* src/index.css */

/* --- Global Resets and Font --- */
body {
  margin: 0;
  font-family: 'HCLTech Roobert', -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", <PERSON>l, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f7f9fc; /* A light background for the whole app */
}

/* --- Common Styles for All Content Pages --- */

/* The main container for pages like SimpleLandingPage, etc. */
.page-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  box-sizing: border-box;
  text-align: center;
}

/* The main title on each content page */
.page-title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

/* The paragraph content on each content page */
.page-content {
  font-size: 1.2rem;
  color: #555;
  margin-bottom: 2rem;
  max-width: 600px;
}

/* The "Go Back" link on each content page */
.back-link {
  font-size: 1rem;
  color: #007bff;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border: 1px solid #007bff;
  border-radius: 5px;
  transition: background-color 0.2s, color 0.2s;
}

.back-link:hover {
  background-color: #007bff;
  color: #fff;
}