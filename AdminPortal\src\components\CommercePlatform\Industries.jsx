import React, { useState, useEffect, useRef } from 'react';
// import './Industries.css';
import { industryIconList } from '../../data/industryIconMap';

// Vite: Import all PNGs in the industry-icons folder as a map
const iconImages = import.meta.glob('../../assets/Images/industry-icons/*.png', { eager: true, as: 'url' });
const industryImages = import.meta.glob('../../assets/Images/industries/*', { eager: true, as: 'url' });

const CARDS_PER_SECTION = 3;
const AUTO_SCROLL_INTERVAL = 3000;
const AUTO_SCROLL_RESUME_DELAY = 5000;

const Industries = ({ onIndustrySelect }) => {
    const [view, setView] = useState('marquee');
    const gridRef = useRef(null);
    const [scrollProgress, setScrollProgress] = useState(0);
    const [activeSection, setActiveSection] = useState(0);
    const [isPaused, setIsPaused] = useState(false);
    const [isCardHovered, setIsCardHovered] = useState(false); // Track hover state
    const autoScrollTimeout = useRef(null);
    const autoScrollInterval = useRef(null);

    const numSections = Math.ceil(industryIconList.length / CARDS_PER_SECTION);

    // Scroll to a section by index
    const scrollToSection = (sectionIdx) => {
        const grid = gridRef.current;
        if (!grid) return;
        const card = grid.querySelector('.industry-card');
        if (!card) return;
        const cardWidth = card.offsetWidth + parseInt(getComputedStyle(grid).gap || 0, 10);
        const scrollLeft = sectionIdx * cardWidth * CARDS_PER_SECTION;
        grid.scrollTo({ left: scrollLeft, behavior: 'smooth' });
    };

    // Handle wheel and scroll events
    useEffect(() => {
        const grid = gridRef.current;
        if (!grid) return;
        let userInteracted = false;
        const onWheel = (e) => {
            if (e.deltaY !== 0) {
                e.preventDefault();
                grid.scrollBy({ left: e.deltaY * 10, behavior: 'auto' });
                pauseAutoScroll();
            }
        };
        const onScroll = () => {
            if (!grid) return;
            const maxScroll = grid.scrollWidth - grid.clientWidth;
            const percent = maxScroll > 0 ? (grid.scrollLeft / maxScroll) : 0;
            setScrollProgress(percent);
            // Update active section
            const card = grid.querySelector('.industry-card');
            if (card) {
                const cardWidth = card.offsetWidth + parseInt(getComputedStyle(grid).gap || 0, 10);
                const sectionIdx = Math.round(grid.scrollLeft / (cardWidth * CARDS_PER_SECTION));
                setActiveSection(Math.min(sectionIdx, numSections - 1));
            }
            pauseAutoScroll(); // Pause auto-scroll on manual scroll
        };
        const onMouseEnter = () => pauseAutoScroll();
        const onMouseLeave = () => resumeAutoScroll();
        grid.addEventListener('wheel', onWheel, { passive: false });
        grid.addEventListener('scroll', onScroll);
        grid.addEventListener('mouseenter', onMouseEnter);
        grid.addEventListener('mouseleave', onMouseLeave);
        // Set initial progress
        onScroll();
        return () => {
            grid.removeEventListener('wheel', onWheel);
            grid.removeEventListener('scroll', onScroll);
            grid.removeEventListener('mouseenter', onMouseEnter);
            grid.removeEventListener('mouseleave', onMouseLeave);
        };
    }, [numSections]);

    // Auto-scroll logic
    useEffect(() => {
        if (isPaused || isCardHovered) return;
        autoScrollInterval.current = setInterval(() => {
            setActiveSection((prev) => {
                const next = (prev + 1) % numSections;
                scrollToSection(next);
                return next;
            });
        }, AUTO_SCROLL_INTERVAL);
        return () => clearInterval(autoScrollInterval.current);
    }, [isPaused, isCardHovered, numSections]);

    // Pause/resume auto-scroll helpers
    const pauseAutoScroll = () => {
        setIsPaused(true);
        if (autoScrollTimeout.current) clearTimeout(autoScrollTimeout.current);
        autoScrollTimeout.current = setTimeout(() => setIsPaused(false), AUTO_SCROLL_RESUME_DELAY);
    };
    const resumeAutoScroll = () => {
        if (autoScrollTimeout.current) clearTimeout(autoScrollTimeout.current);
        setIsPaused(false);
    };

    // Handle click/hover on progress bar section
    const handleSectionAction = (idx) => {
        pauseAutoScroll();
        scrollToSection(idx);
        setActiveSection(idx);
    };

    // Duplicate the data for a seamless loop
    const sortedIndustries = [...industryIconList].sort((a, b) => a.name.localeCompare(b.name));
    const extendedIndustries = [...sortedIndustries, ...sortedIndustries];

    return (
        <section id="industries" className="industries-section">
            <div className="section-header section-header-no-margin">
                <h2 className="section-title">Powering Every Major Industry</h2>
                <div className="industries-header-row">
                    <p className="section-subtitle section-subtitle-no-margin">
                        Our platform is trusted by leaders in a wide range of sectors for its flexibility and power.
                    </p>
                </div>
            </div>
            <div ref={gridRef} className="industry-cards-grid">
                {[...industryIconList].sort((a, b) => a.name.localeCompare(b.name)).map((industry) => {
                    // Prefer .image from /industries, fallback to .icons from /industry-icons
                    let imgSrc = '';
                    if (industry.image && industryImages[`../../assets/Images/industries/${industry.image}`]) {
                        imgSrc = industryImages[`../../assets/Images/industries/${industry.image}`];
                    } else if (industry.icons && iconImages[`../../assets/Images/industry-icons/${industry.icons}`]) {
                        imgSrc = iconImages[`../../assets/Images/industry-icons/${industry.icons}`];
                    }
                    return (
                        <div className="industry-card" key={industry.id} onClick={() => onIndustrySelect && onIndustrySelect(industry.name)} style={{ cursor: 'pointer' }}
                            onMouseEnter={() => setIsCardHovered(true)}
                            onMouseLeave={() => setIsCardHovered(false)}
                        >
                            <div className="industry-card-content">
                                <h3 className="industry-card-title">{industry.name}</h3>
                                <p className="industry-card-desc">{industry.desc}</p>
                            </div>
                            <div className="industry-card-img-wrap">
                                {imgSrc ? (
                                    <img
                                        src={imgSrc}
                                        alt={industry.name}
                                        className="industry-card-img"
                                    />
                                ) : null}
                            </div>
                        </div>
                    );
                })}
            </div>
            {/* Segmented progress bar with smooth fill */}
            <div className="industry-scroll-progress" style={{ position: 'relative', display: 'flex', alignItems: 'center', gap: 0 }}>
                {/* Filled bar */}
                <div
                    className="industry-scroll-progress-bar"
                    style={{
                        position: 'absolute',
                        left: 0,
                        top: 0,
                        height: '100%',
                        width: `${((activeSection + 1) / numSections) * 100}%`,
                        background: 'linear-gradient(90deg, var(--primary-teal), var(--primary-blue))',
                        borderRadius: 'var(--radius-full)',
                        transition: 'width 0.4s cubic-bezier(.4,1.3,.5,1)',
                        zIndex: 1,
                    }}
                />
                {/* Segments as clickable overlays */}
                {Array.from({ length: numSections }).map((_, idx) => (
                    <div
                        key={idx}
                        className="industry-scroll-progress-segment"
                        style={{
                            flex: 1,
                            height: '100%',
                            zIndex: 2,
                            cursor: 'pointer',
                            position: 'relative',
                        }}
                        onClick={() => handleSectionAction(idx)}
                    />
                ))}
            </div>
        </section>
    );
};

export default Industries; 