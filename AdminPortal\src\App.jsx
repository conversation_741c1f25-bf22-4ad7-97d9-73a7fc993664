import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider as MuiThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box } from '@mui/material';

// --- Static Page Imports ---
import Header from './components/layout/CommerceLayout/Header';
import Footer from './components/layout/CommerceLayout/Footer';
import Hero from './components/CommercePlatform/Hero';
import Industries from './components/CommercePlatform/Industries';
import Solutions from './components/CommercePlatform/Solutions';
import Partners from './components/CommercePlatform/Partners';
import Pricing from './components/CommercePlatform/Pricing';
import Faq from './components/CommercePlatform/Faq';
import AboutUs from './components/CommercePlatform/AboutUs';
import Contact from './components/CommercePlatform/Contact';
import Cart from './components/CommercePlatform/Cart';
import AuthModal from './components/common/AuthModal';
import FabMenu from './components/common/FabMenu';
import AssistantFab from './components/common/AssistantFab';

// --- Admin Dashboard Imports ---
import './App.css';
import { theme as baseTheme, AdminComponents } from './styles/theme';
import TopBar from './components/layout/Adminlayout/TopBar'; 
import AdminNavigation from './components/layout/Adminlayout/AdminNavigation';
import AdminFooter from './components/layout/Adminlayout/Footer';
import Breadcrumbs from './components/layout/Adminlayout/Breadcrumbs';
import ErrorBoundary from './components/layout/Adminlayout/ErrorBoundary';

// --- Admin Page Imports ---
import HomeAdmin from './components/AdminComponents/Dashboard/HclDashboard';
import Customers from './components/AdminComponents/Masters/Customers';
import Contract from './components/AdminComponents/Transaction/Contract';
import Purchase from './components/AdminComponents/Transaction/Purchase';
import Receipts from './components/AdminComponents/Transaction/Receipts';
import Invoices from './components/AdminComponents/Transaction/Invoices';
import License from './components/AdminComponents/Masters/License';
import Server from './components/AdminComponents/Masters/Server';
import Travel from './components/AdminComponents/Masters/Travel';
import Support from './components/AdminComponents/Masters/Support';
import SLA from './components/AdminComponents/Masters/SLA';
import Professional from './components/AdminComponents/Masters/Professional';


import User from './components/AdminComponents/Masters/User';


// --- Constants ---
const defaultAdmin = {
  email: '<EMAIL>',
  password: '12345',
  type: 'admin',
  name: 'Admin',
  country: 'India',
  role: 'Admin',
  company: 'HCLTech',
  phone: '**********',
  address: '123, Main St, Anytown, USA',
  photo: 'src/assets/Images/social-icons/user.png',
};

function App() {
  // --- STATE MANAGEMENT ---
  const [layoutMode, setLayoutMode] = useState('sidebar'); 
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [user, setUser] = useState(null);
  const [authReady, setAuthReady] = useState(false); // FIX: State to track if auth check is complete
  const [isDefaultUserLoggedIn, setIsDefaultUserLoggedIn] = useState(true);
  const [cart, setCart] = useState([]);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState('success');
  const [approvalModal, setApprovalModal] = useState({ open: false, order: null, type: null });
  const [selectedIndustry, setSelectedIndustry] = useState(null);

  // --- EFFECT FOR INITIAL USER LOAD ---
  useEffect(() => {
    let activeUser = null;
    try {
      const stored = localStorage.getItem('user');
      if (stored) {
        const parsed = JSON.parse(stored);
        if (parsed && parsed.loginTimestamp) {
          const now = Date.now();
          if (now - parsed.loginTimestamp < 10 * 60 * 1000) {
            activeUser = parsed;
          } else {
            localStorage.removeItem('user');
          }
        }
      }
    } catch (error) {
      console.error("Failed to parse user from localStorage:", error);
      localStorage.removeItem('user');
    }

    if (!activeUser && window.location.pathname.startsWith('/admin')) {
      const adminUser = { ...defaultAdmin, loginTimestamp: Date.now() };
      localStorage.setItem('user', JSON.stringify(adminUser));
      activeUser = adminUser;
    }
    
    setUser(activeUser);
    setAuthReady(true); // FIX: Signal that auth check is done
  }, []);

  // --- THEME AND LAYOUT ---
  const toggleTheme = () => setIsDarkMode(prev => !prev);
  
  const activeTheme = createTheme({
    ...baseTheme,
    palette: {
        ...baseTheme.palette,
        mode: isDarkMode ? 'dark' : 'light',
        background: {
            default: isDarkMode ? '#111827' : 'var(--background-primary)',
            paper: isDarkMode ? '#1f2937' : 'var(--background-secondary)',
        }
    }
  });

  // --- HANDLERS AND EFFECTS ---
  const handleIndustrySelect = (industryName) => {
    setSelectedIndustry(industryName);
    setTimeout(() => {
      const el = document.getElementById('solutions');
      if (el) {
        const y = el.getBoundingClientRect().top + window.pageYOffset - 60;
        window.scrollTo({ top: y, behavior: 'smooth' });
      }
    }, 100);
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('user');
    setIsDefaultUserLoggedIn(false);
    setCart([]);
    setMessage('You have been logged out successfully.');
    setMessageType('success');
    setTimeout(() => setMessage(''), 3000);
    window.location.href = '/';
  };

  const handleAddToCart = (item, type) => {
    if (user) {
      const identifier = type === 'module' ? item.id : item.name;
      const exists = cart.some(
        c => c.type === type && (type === 'module' ? c.id === identifier : c.name === identifier)
      );
      if (!exists) {
        setCart(c => [...c, type === 'module' ? { type, id: item.id } : { type, name: item.name }]);
        setMessage(type === 'module' ? `${item.name} added to cart.` : `${item.name} suite added to cart.`);
        setMessageType('success');
      } else {
        setMessage(type === 'module' ? `${item.name} is already in the cart.` : `${item.name} suite is already in the cart.`);
        setMessageType('warning');
      }
      setTimeout(() => setMessage(''), 2000);
    } else {
      setShowAuthModal(true);
    }
  };

  const cartCount = cart.length;

  const handleLogin = (userObj) => {
    const userWithTimestamp = { ...userObj, loginTimestamp: Date.now() };
    setUser(userWithTimestamp);
    localStorage.setItem('user', JSON.stringify(userWithTimestamp));
    setShowAuthModal(false);
  };

  useEffect(() => {
    if (user && user.type === 'admin' && !window.location.pathname.startsWith('/admin')) {
      window.location.href = '/admin';
    }
  }, [user]);

  // --- RENDER ---
  // FIX: Don't render anything until the auth check is complete
  if (!authReady) {
    return <div />; // Or a loading spinner
  }

  return (
    <MuiThemeProvider theme={activeTheme}>
        <CssBaseline />
        <Router>
            {message && (
                <AdminComponents.MessageContainer messageType={messageType}>
                <AdminComponents.MessageIcon>
                    {messageType === 'success' ? '\u2705' : '\u26a0\ufe0f'}
                </AdminComponents.MessageIcon>
                <span>{message}</span>
                </AdminComponents.MessageContainer>
            )}
            <Routes>
                {/* Admin Routes */}
                <Route path="/admin/*" element={
                    <AdminComponents.AdminLayoutContainer>
                        <TopBar
                            logout={logout}
                            layoutMode={layoutMode}
                            setLayoutMode={setLayoutMode}
                            toggleTheme={toggleTheme}
                            isDarkMode={isDarkMode}
                        />
                        <AdminComponents.AdminMainBody>
                            {layoutMode === 'sidebar' && <AdminNavigation mode="sidebar" />}
                            <AdminComponents.AdminContentContainer>
                                {layoutMode === 'topbar' && <AdminNavigation mode="topbar" />}
                                <AdminComponents.AdminMainContent component="main">
                                    <Breadcrumbs />
                                    <ErrorBoundary>
                                        <Routes>
                                            <Route path="/" element={<HomeAdmin />} />
                                            <Route path="/contract" element={<Contract />} />
                                            <Route path="/license" element={<License />} />
                                            <Route path="/server" element={<Server />} />
                                            <Route path="/travel" element={<Travel />} />
                                            <Route path="/support" element={<Support />} />
                                            <Route path="/purchase" element={<Purchase />} />
                                            <Route path="/sla" element={<SLA />} />
                                            <Route path="/professional" element={<Professional />} />
                                            <Route path="/OnBoarding" element={<Customers />} />
                                            <Route path="/receipts" element={<Receipts />} />
                                            <Route path="/invoices" element={<Invoices />} />
                                            <Route path="/user" element={<User />} />
                                        </Routes>
                                    </ErrorBoundary>
                                </AdminComponents.AdminMainContent>
                            </AdminComponents.AdminContentContainer>
                        </AdminComponents.AdminMainBody>
                        <AdminFooter />
                    </AdminComponents.AdminLayoutContainer>
                } />

                <Route path="/" element={
                    <>
                    <Header user={user} setUser={setUser} logout={logout} cartCount={cartCount} showAuthModal={showAuthModal} setShowAuthModal={setShowAuthModal} onLogin={handleLogin} />
                    <main>
                        <Hero />
                        <Industries onIndustrySelect={handleIndustrySelect} />
                        <Solutions handleAddToCart={handleAddToCart} selectedIndustry={selectedIndustry} onClearIndustry={() => setSelectedIndustry(null)} />
                        <Partners />
                        <Pricing />
                        <Faq />
                        <AboutUs />
                        <Contact />
                    </main>
                    <Footer />
                    </>
                } />
                <Route path="/cart" element={
                    <Cart
                    user={user}
                    setUser={setUser}
                    logout={logout}
                    cartCount={cartCount}
                    showAuthModal={showAuthModal}
                    setShowAuthModal={setShowAuthModal}
                    isDefaultUserLoggedIn={isDefaultUserLoggedIn}
                    setIsDefaultUserLoggedIn={setIsDefaultUserLoggedIn}
                    onLogin={handleLogin}
                    />
                } />
            </Routes>
            {showAuthModal && (
                <AuthModal mode={"login"} onClose={() => setShowAuthModal(false)} onLogin={handleLogin} />
            )}
            <FabMenu isAdmin={window.location.pathname.startsWith('/admin')} />
            {/* <AssistantFab /> */}
        </Router>
    </MuiThemeProvider>
  );
}

export default App;
