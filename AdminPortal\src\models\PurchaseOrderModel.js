/**
 * Purchase Order Data Model
 * Defines the structure and validation for Purchase Order data
 */

// Default empty purchase order object
export const emptyPurchaseOrder = {
  // Company Information
  owner: '',
  parentCompanyName: '',
  accountName: '',
  domainVertical: '',
  region: '',
  
  // Financial Details
  revenueType: '',
  poNumber: '',
  poCurrency: 'INR',
  poValue: 0,
  conversionRate: 1,
  hclConversionRateUSD: 83.5, // Default HCL conversion rate
  
  // Timeline Information
  opportunityStage: '',
  frequencyOfRealization: 'Monthly',
  startDate: '',
  endDate: '',
  
  // Product Details
  renewalUpsell: 'New',
  productRevenueCategory: '',
  productName: '',
  projectDescription: '',
  
  // Calculated Fields (these will be computed)
  poFunnelValueINR: 0,
  usdEquivalent: 0,
  grandTotalINR: 0,
  grandTotalUSD: 0,
  fy2025Total: 0,
  
  // Monthly Tracking - Projected
  projectedApr25: 0,
  projectedMay25: 0,
  projectedJun25: 0,
  projectedJul25: 0,
  projectedAug25: 0,
  projectedSep25: 0,
  projectedOct25: 0,
  projectedNov25: 0,
  projectedDec25: 0,
  projectedJan26: 0,
  projectedFeb26: 0,
  projectedMar26: 0,
  
  // Monthly Tracking - Actual
  actualApr25: 0,
  actualMay25: 0,
  actualJun25: 0,
  actualJul25: 0,
  actualAug25: 0,
  actualSep25: 0,
  actualOct25: 0,
  actualNov25: 0,
  actualDec25: 0,
  actualJan26: 0,
  actualFeb26: 0,
  actualMar26: 0,
  
  // Quarterly Summaries - Projected (will be computed)
  projectedAMJ25: 0,
  projectedJAS25: 0,
  projectedOND25: 0,
  projectedJFM26: 0,
  
  // Quarterly Summaries - Actual (will be computed)
  actualAMJ25: 0,
  actualJAS25: 0,
  actualOND25: 0,
  actualJFM26: 0,
  
  // Status and metadata
  status: 'Draft',
  createdBy: '',
  createdDate: '',
  lastModifiedBy: '',
  lastModifiedDate: '',
};

// Field definitions with validation rules
export const fieldDefinitions = {
  // Company Information
  owner: { 
    type: 'string', 
    required: true,
    label: 'labels.owner'
  },
  parentCompanyName: { 
    type: 'string', 
    required: true,
    label: 'labels.parentCompany'
  },
  accountName: { 
    type: 'string', 
    required: true,
    label: 'labels.accountName'
  },
  domainVertical: { 
    type: 'string', 
    required: true,
    label: 'labels.domain'
  },
  region: { 
    type: 'string', 
    required: true,
    label: 'labels.region'
  },
  
  // Financial Details
  revenueType: { 
    type: 'string', 
    required: true,
    label: 'labels.revenueType'
  },
  poNumber: { 
    type: 'string', 
    required: true,
    label: 'labels.poNumber'
  },
  poCurrency: { 
    type: 'string', 
    required: true,
    label: 'labels.poCurrency'
  },
  poValue: { 
    type: 'number', 
    required: true,
    min: 0,
    label: 'labels.poValue'
  },
  conversionRate: { 
    type: 'number', 
    required: true,
    min: 0,
    label: 'labels.conversionRate'
  },
  hclConversionRateUSD: { 
    type: 'number', 
    required: true,
    min: 0,
    label: 'labels.hclConversionRate'
  },
  
  // Timeline Information
  opportunityStage: { 
    type: 'string', 
    required: true,
    label: 'labels.opportunityStage'
  },
  frequencyOfRealization: { 
    type: 'string', 
    required: true,
    label: 'labels.frequency'
  },
  startDate: { 
    type: 'date', 
    required: true,
    label: 'labels.startDate'
  },
  endDate: { 
    type: 'date', 
    required: true,
    label: 'labels.endDate'
  },
  
  // Product Details
  renewalUpsell: { 
    type: 'string', 
    required: true,
    label: 'labels.renewalUpsell'
  },
  productRevenueCategory: { 
    type: 'string', 
    required: true,
    label: 'labels.productCategory'
  },
  productName: { 
    type: 'string', 
    required: true,
    label: 'labels.productName'
  },
  projectDescription: { 
    type: 'string', 
    required: false,
    label: 'labels.projectDescription'
  }
};

// Dropdown options for select fields
export const dropdownOptions = {
  region: ['North America', 'Europe', 'Asia Pacific', 'Latin America', 'Middle East & Africa'],
  revenueType: ['License', 'Services', 'Support', 'Subscription', 'Hardware'],
  poCurrency: ['INR', 'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD'],
  opportunityStage: ['Prospecting', 'Qualification', 'Needs Analysis', 'Value Proposition', 'Decision Makers', 'Proposal/Price Quote', 'Negotiation/Review', 'Closed Won', 'Closed Lost'],
  frequencyOfRealization: ['Monthly', 'Quarterly', 'Annually', 'One-time'],
  renewalUpsell: ['New', 'Renewal', 'Upsell', 'Cross-sell'],
  productRevenueCategory: ['Software', 'Hardware', 'Professional Services', 'Managed Services', 'Cloud Services', 'Training'],
  status: ['Draft', 'Pending', 'Approved', 'Rejected', 'Completed', 'Cancelled']
};

// Helper function to calculate derived values
export const calculateDerivedValues = (po) => {
  // Calculate INR value
  const poFunnelValueINR = po.poValue * po.conversionRate;
  
  // Calculate USD equivalent
  const usdEquivalent = poFunnelValueINR / po.hclConversionRateUSD;
  
  // Calculate quarterly projected totals
  const projectedAMJ25 = po.projectedApr25 + po.projectedMay25 + po.projectedJun25;
  const projectedJAS25 = po.projectedJul25 + po.projectedAug25 + po.projectedSep25;
  const projectedOND25 = po.projectedOct25 + po.projectedNov25 + po.projectedDec25;
  const projectedJFM26 = po.projectedJan26 + po.projectedFeb26 + po.projectedMar26;
  
  // Calculate quarterly actual totals
  const actualAMJ25 = po.actualApr25 + po.actualMay25 + po.actualJun25;
  const actualJAS25 = po.actualJul25 + po.actualAug25 + po.actualSep25;
  const actualOND25 = po.actualOct25 + po.actualNov25 + po.actualDec25;
  const actualJFM26 = po.actualJan26 + po.actualFeb26 + po.actualMar26;
  
  // Calculate grand totals
  const grandTotalINR = projectedAMJ25 + projectedJAS25 + projectedOND25 + projectedJFM26;
  const grandTotalUSD = grandTotalINR / po.hclConversionRateUSD;
  
  // Calculate FY 2025-26 total
  const fy2025Total = grandTotalINR;
  
  return {
    ...po,
    poFunnelValueINR,
    usdEquivalent,
    projectedAMJ25,
    projectedJAS25,
    projectedOND25,
    projectedJFM26,
    actualAMJ25,
    actualJAS25,
    actualOND25,
    actualJFM26,
    grandTotalINR,
    grandTotalUSD,
    fy2025Total
  };
};

// Validation function
export const validatePurchaseOrder = (po) => {
  const errors = {};
  
  Object.entries(fieldDefinitions).forEach(([field, definition]) => {
    // Check required fields
    if (definition.required && !po[field]) {
      errors[field] = 'validation.required';
    }
    
    // Check min values for numbers
    if (definition.type === 'number' && definition.min !== undefined && po[field] < definition.min) {
      errors[field] = 'validation.minValue';
    }
    
    // Check date validity
    if (definition.type === 'date' && po[field] && isNaN(new Date(po[field]).getTime())) {
      errors[field] = 'validation.invalidDate';
    }
  });
  
  // Check that end date is after start date
  if (po.startDate && po.endDate && new Date(po.endDate) < new Date(po.startDate)) {
    errors.endDate = 'End date must be after start date';
  }
  
  return errors;
};
