import React, { useState, useMemo } from 'react';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    ToggleButton, FormControlLabel, Drawer, Chip, FormControl, InputLabel, Select, MenuItem,
    Tabs, Tab, Grid, Dialog, DialogContent, DialogActions
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, People, CheckCircle, Cancel, WarningAmber, ShowChart, PieChart, DonutLarge,
    Receipt, Payment, Settings, Save, Close, Upload, CloudUpload, AccountBalance, Assessment,
    Description, Download, Print, AttachMoney
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';

// Mock receipt data based on paid invoices
const initialReceipts = [
  {
    id: 'rec-001',
    receiptNumber: 'REC-2025-001',
    invoiceNumber: 'INV-2025-001',
    accountName: 'Acme Global Services',
    parentCompanyName: 'Acme Corporation',
    productName: 'HCL Commerce Suite',
    receiptDate: '2025-04-20',
    paymentDate: '2025-04-20',
    amount: 8333.33,
    currency: 'USD',
    amountINR: 695833.33,
    paymentMethod: 'Bank Transfer',
    transactionId: 'TXN-2025-001',
    bankReference: 'BNK-REF-001',
    status: 'Confirmed',
    region: 'North America',
    domainVertical: 'Manufacturing',
    createdBy: 'Jane Doe',
    createdDate: '2025-04-20T10:30:00Z'
  },
  {
    id: 'rec-002',
    receiptNumber: 'REC-2025-002',
    invoiceNumber: 'INV-2025-003',
    accountName: 'Global Manufacturing Services',
    parentCompanyName: 'Global Manufacturing Ltd.',
    productName: 'HCL Manufacturing Suite',
    receiptDate: '2025-05-15',
    paymentDate: '2025-05-15',
    amount: 10000,
    currency: 'INR',
    amountINR: 10000,
    paymentMethod: 'Credit Card',
    transactionId: 'TXN-2025-002',
    bankReference: 'CC-REF-002',
    status: 'Confirmed',
    region: 'Asia',
    domainVertical: 'Manufacturing',
    createdBy: 'Alex Turner',
    createdDate: '2025-05-15T09:00:00Z'
  },
  {
    id: 'rec-003',
    receiptNumber: 'REC-2025-003',
    invoiceNumber: 'INV-2025-004',
    accountName: 'Energy Solutions',
    parentCompanyName: 'Energy Solutions Inc.',
    productName: 'HCL Energy Suite',
    receiptDate: '2025-07-20',
    paymentDate: '2025-07-20',
    amount: 9166.67,
    currency: 'EUR',
    amountINR: 826833.33,
    paymentMethod: 'Wire Transfer',
    transactionId: 'TXN-2025-003',
    bankReference: 'WIRE-REF-003',
    status: 'Pending Verification',
    region: 'Europe',
    domainVertical: 'Energy',
    createdBy: 'Maria Lopez',
    createdDate: '2025-07-20T13:00:00Z'
  },
  {
    id: 'rec-004',
    receiptNumber: 'REC-2025-004',
    invoiceNumber: 'INV-2025-005',
    accountName: 'Logistics Corp. APAC',
    parentCompanyName: 'Logistics Corp.',
    productName: 'HCL Logistics Suite',
    receiptDate: '2025-09-15',
    paymentDate: '2025-09-15',
    amount: 8750,
    currency: 'INR',
    amountINR: 8750,
    paymentMethod: 'Online Payment',
    transactionId: 'TXN-2025-004',
    bankReference: 'ONL-REF-004',
    status: 'Confirmed',
    region: 'Asia',
    domainVertical: 'Logistics',
    createdBy: 'Emily Chen',
    createdDate: '2025-09-15T15:30:00Z'
  }
];

const ALL_COLUMNS = [
  { key: 'receiptNumber', label: 'Receipt Number', type: 'string', groupable: true },
  { key: 'invoiceNumber', label: 'Invoice Number', type: 'string', groupable: true },
  { key: 'accountName', label: 'Account Name', type: 'string', groupable: true },
  { key: 'productName', label: 'Product Name', type: 'string', groupable: true },
  { key: 'receiptDate', label: 'Receipt Date', type: 'date', groupable: true },
  { key: 'amountINR', label: 'Amount (INR)', type: 'number', groupable: false },
  { key: 'paymentMethod', label: 'Payment Method', type: 'string', groupable: true },
  { key: 'status', label: 'Status', type: 'string', groupable: true },
];

const FILTER_OPERATORS = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'];

const ActionButtons = ({ receipt, onView, onEdit, onDelete }) => (
    <Box onClick={e => e.stopPropagation()}>
        <IconButton size="small" onClick={() => onView(receipt)} title="View Details">
            <Visibility fontSize="small" />
        </IconButton>
        <IconButton size="small" onClick={() => onEdit(receipt)} title="Edit">
            <Edit fontSize="small" />
        </IconButton>
        <IconButton size="small" color="error" onClick={() => onDelete([receipt.id])} title="Delete">
            <Delete fontSize="small" />
        </IconButton>
        <IconButton size="small" onClick={() => window.print()} title="Print Receipt">
            <Print fontSize="small" />
        </IconButton>
    </Box>
);

const ReceiptCard = ({ receipt, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
  <AdminComponents.CardBase isSelected={isSelected}>
    <Checkbox
      className="card-checkbox"
      checked={isChecked}
      onChange={() => onSelect(receipt.id)}
      onClick={e => e.stopPropagation()}
    />
    <AdminComponents.CardActionContainer>
      <ActionButtons receipt={receipt} onView={onView} onEdit={onEdit} onDelete={onDelete} />
    </AdminComponents.CardActionContainer>
    <AdminComponents.PaddedCardContent>
      <Typography variant="h6" component="div" noWrap>{receipt.receiptNumber}</Typography>
      <Typography color="text.secondary" noWrap gutterBottom>{receipt.accountName}</Typography>
      <AdminComponents.StatusBadge
        ownerState={{ status: receipt.status }}
        label={receipt.status}
        size="small"
      />
      <AdminComponents.CardDivider />
      <AdminComponents.CardDetailsGrid>
        <AdminComponents.CardDetailLabel variant="body2">Invoice:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>{receipt.invoiceNumber}</Typography>
        <AdminComponents.CardDetailLabel variant="body2">Product:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>{receipt.productName}</Typography>
        <AdminComponents.CardDetailLabel variant="body2">Amount:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>
          {receipt.amountINR.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
        </Typography>
        <AdminComponents.CardDetailLabel variant="body2">Payment Method:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>{receipt.paymentMethod}</Typography>
      </AdminComponents.CardDetailsGrid>
    </AdminComponents.PaddedCardContent>
  </AdminComponents.CardBase>
);

const ReceiptCompactCard = ({ receipt, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
  <AdminComponents.CardBase isSelected={isSelected}>
    <Checkbox
      className="card-checkbox"
      checked={isChecked}
      onChange={() => onSelect(receipt.id)}
      onClick={e => e.stopPropagation()}
    />
    <AdminComponents.CardActionContainer>
      <ActionButtons receipt={receipt} onView={onView} onEdit={onEdit} onDelete={onDelete} />
    </AdminComponents.CardActionContainer>
    <AdminComponents.CompactCardContent>
      <div>
        <Typography variant="subtitle1" fontWeight="bold" noWrap>{receipt.receiptNumber}</Typography>
        <Typography variant="caption" color="text.secondary">{receipt.accountName}</Typography>
      </div>
      <AdminComponents.CompactCardFooter>
        <Typography variant="body2" fontWeight="500">
          {receipt.amountINR.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
        </Typography>
        <AdminComponents.StatusBadge
          ownerState={{ status: receipt.status }}
          label={receipt.status}
          size="small"
        />
      </AdminComponents.CompactCardFooter>
    </AdminComponents.CompactCardContent>
  </AdminComponents.CardBase>
);

const ReceiptListItem = ({ receipt, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
  <AdminComponents.ListItemCard isSelected={isSelected}>
    <AdminComponents.ListItemGrid>
      <Checkbox
        checked={isChecked}
        onChange={() => onSelect(receipt.id)}
        onClick={e => e.stopPropagation()}
      />
      <Box>
        <Typography fontWeight="bold">{receipt.receiptNumber}</Typography>
        <Typography variant="body2" color="text.secondary">{receipt.accountName}</Typography>
      </Box>
      <Typography variant="body2">{receipt.invoiceNumber}</Typography>
      <Typography variant="body2">{receipt.productName}</Typography>
      <Typography variant="body2">
        {receipt.amountINR.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
      </Typography>
      <Typography variant="body2">{receipt.paymentMethod}</Typography>
      <AdminComponents.StatusBadge
        ownerState={{ status: receipt.status }}
        label={receipt.status}
        size="small"
      />
      <AdminComponents.ListItemActions>
        <ActionButtons receipt={receipt} onView={onView} onEdit={onEdit} onDelete={onDelete} />
      </AdminComponents.ListItemActions>
    </AdminComponents.ListItemGrid>
  </AdminComponents.ListItemCard>
);

const ReceiptTable = ({
  receipts,
  onRowClick,
  onHeaderClick,
  sortColumn,
  sortDirection,
  selectedId,
  selectedIds,
  onSelectAll,
  onSelectOne,
  columnOrder,
  setColumnOrder,
  onDelete,
  onEdit,
  onView
}) => {
    const dragItemIndex = React.useRef(null);
    const dragOverItemIndex = React.useRef(null);

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };

    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox">
                            <Checkbox
                                indeterminate={selectedIds.length > 0 && selectedIds.length < receipts.length}
                                checked={receipts.length > 0 && selectedIds.length === receipts.length}
                                onChange={onSelectAll}
                            />
                        </TableCell>
                        {columnOrder.map((colKey, idx) => (
                            <TableCell
                                key={colKey}
                                draggable
                                onDragStart={() => { dragItemIndex.current = idx; }}
                                onDragEnter={() => { dragOverItemIndex.current = idx; }}
                                onDragEnd={handleDrop}
                                onDragOver={e => e.preventDefault()}
                                style={{ cursor: 'move' }}
                            >
                                <TableSortLabel
                                    active={sortColumn === colKey}
                                    direction={sortDirection}
                                    onClick={() => onHeaderClick(colKey)}
                                >
                                    {ALL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </TableCell>
                        ))}
                        <TableCell align="center">Actions</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {receipts.map(receipt => (
                        <TableRow
                            key={receipt.id}
                            hover
                            selected={selectedId === receipt.id}
                            onClick={() => onRowClick(receipt)}
                        >
                            <TableCell padding="checkbox">
                                <Checkbox
                                    checked={selectedIds.includes(receipt.id)}
                                    onChange={() => onSelectOne(receipt.id)}
                                    onClick={e => e.stopPropagation()}
                                />
                            </TableCell>
                            {columnOrder.map(colKey => (
                                <TableCell key={colKey}>
                                    {colKey === 'amountINR'
                                        ? receipt[colKey].toLocaleString('en-IN', { style: 'currency', currency: 'INR' })
                                        : colKey === 'receiptDate'
                                        ? new Date(receipt[colKey]).toLocaleDateString('en-IN')
                                        : receipt[colKey]}
                                </TableCell>
                            ))}
                            <TableCell align="center">
                                <ActionButtons receipt={receipt} onView={onView} onEdit={onEdit} onDelete={onDelete} />
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </AdminComponents.TableViewContainer>
    );
};

// TabPanel utility
const TabPanel = (props) => {
  const { children, value, index, ...other } = props;
  return (
    <div role="tabpanel" hidden={value !== index} id={`tabpanel-${index}`} aria-labelledby={`tab-${index}`} {...other}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const Receipts = () => {
    const [receipts, setReceipts] = useState(initialReceipts);
    const [filteredReceipts, setFilteredReceipts] = useState(initialReceipts);
    const [selectedId, setSelectedId] = useState(null);
    const [selectedIds, setSelectedIds] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [sortColumn, setSortColumn] = useState('receiptDate');
    const [sortDirection, setSortDirection] = useState('desc');
    const [viewMode, setViewMode] = useState('table');
    const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(col => col.key));
    const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
    const [tabValue, setTabValue] = useState(0);

    // Filter and search logic
    const handleSearch = (term) => {
        setSearchTerm(term);
        if (!term.trim()) {
            setFilteredReceipts(receipts);
            return;
        }

        const filtered = receipts.filter(receipt =>
            Object.values(receipt).some(value =>
                value && value.toString().toLowerCase().includes(term.toLowerCase())
            )
        );
        setFilteredReceipts(filtered);
    };

    // Sorting logic
    const handleSort = (column) => {
        const isAsc = sortColumn === column && sortDirection === 'asc';
        const direction = isAsc ? 'desc' : 'asc';
        setSortColumn(column);
        setSortDirection(direction);

        const sorted = [...filteredReceipts].sort((a, b) => {
            if (column === 'amountINR') {
                return direction === 'asc' ? a[column] - b[column] : b[column] - a[column];
            }
            if (column === 'receiptDate') {
                return direction === 'asc'
                    ? new Date(a[column]) - new Date(b[column])
                    : new Date(b[column]) - new Date(a[column]);
            }
            const aVal = a[column]?.toString().toLowerCase() || '';
            const bVal = b[column]?.toString().toLowerCase() || '';
            return direction === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
        });

        setFilteredReceipts(sorted);
    };

    // Selection handlers
    const handleSelectAll = (event) => {
        if (event.target.checked) {
            setSelectedIds(filteredReceipts.map(receipt => receipt.id));
        } else {
            setSelectedIds([]);
        }
    };

    const handleSelectOne = (id) => {
        setSelectedIds(prev =>
            prev.includes(id) ? prev.filter(selectedId => selectedId !== id) : [...prev, id]
        );
    };

    const handleRowClick = (receipt) => {
        setSelectedId(receipt.id);
    };

    // Action handlers
    const handleView = (receipt) => {
        console.log('View receipt:', receipt);
        // Implement view logic
    };

    const handleEdit = (receipt) => {
        console.log('Edit receipt:', receipt);
        // Implement edit logic
    };

    const handleDelete = (ids) => {
        if (window.confirm(`Are you sure you want to delete ${ids.length} receipt(s)?`)) {
            setReceipts(prev => prev.filter(receipt => !ids.includes(receipt.id)));
            setFilteredReceipts(prev => prev.filter(receipt => !ids.includes(receipt.id)));
            setSelectedIds([]);
            setSelectedId(null);
        }
    };

    const handleAdd = () => {
        console.log('Add new receipt');
        // Implement add logic
    };

    // Summary calculations
    const summaryData = useMemo(() => {
        const totalReceipts = filteredReceipts.length;
        const totalAmount = filteredReceipts.reduce((sum, receipt) => sum + receipt.amountINR, 0);
        const confirmedReceipts = filteredReceipts.filter(r => r.status === 'Confirmed').length;
        const pendingReceipts = filteredReceipts.filter(r => r.status === 'Pending Verification').length;

        return {
            totalReceipts,
            totalAmount,
            confirmedReceipts,
            pendingReceipts,
            averageAmount: totalReceipts > 0 ? totalAmount / totalReceipts : 0
        };
    }, [filteredReceipts]);

    const renderContent = () => (
        <AdminComponents.ViewContainer>
            {filteredReceipts.length > 0 ? (
                <>
                    {viewMode === 'grid' && (
                        <AdminComponents.GridView>
                            {filteredReceipts.map(receipt => (
                                <ReceiptCard
                                    key={receipt.id}
                                    receipt={receipt}
                                    isSelected={selectedId === receipt.id}
                                    onSelect={handleRowClick}
                                    isChecked={selectedIds.includes(receipt.id)}
                                    onDelete={handleDelete}
                                    onEdit={handleEdit}
                                    onView={handleView}
                                />
                            ))}
                        </AdminComponents.GridView>
                    )}
                    {viewMode === 'compact' && (
                        <AdminComponents.CompactView>
                            {filteredReceipts.map(receipt => (
                                <ReceiptCompactCard
                                    key={receipt.id}
                                    receipt={receipt}
                                    isSelected={selectedId === receipt.id}
                                    onSelect={handleRowClick}
                                    isChecked={selectedIds.includes(receipt.id)}
                                    onDelete={handleDelete}
                                    onEdit={handleEdit}
                                    onView={handleView}
                                />
                            ))}
                        </AdminComponents.CompactView>
                    )}
                    {viewMode === 'list' && (
                        <AdminComponents.ListView>
                            {filteredReceipts.map(receipt => (
                                <ReceiptListItem
                                    key={receipt.id}
                                    receipt={receipt}
                                    isSelected={selectedId === receipt.id}
                                    onSelect={handleRowClick}
                                    isChecked={selectedIds.includes(receipt.id)}
                                    onDelete={handleDelete}
                                    onEdit={handleEdit}
                                    onView={handleView}
                                />
                            ))}
                        </AdminComponents.ListView>
                    )}
                    {viewMode === 'table' && (
                        <ReceiptTable
                            receipts={filteredReceipts}
                            onRowClick={handleRowClick}
                            onHeaderClick={handleSort}
                            sortColumn={sortColumn}
                            sortDirection={sortDirection}
                            selectedId={selectedId}
                            selectedIds={selectedIds}
                            onSelectAll={handleSelectAll}
                            onSelectOne={handleSelectOne}
                            columnOrder={columnOrder}
                            setColumnOrder={setColumnOrder}
                            onDelete={handleDelete}
                            onEdit={handleEdit}
                            onView={handleView}
                        />
                    )}
                </>
            ) : (
                <AdminComponents.CenteredMessage component={Paper}>
                    <AdminComponents.LargeIcon color="disabled" />
                    <Typography variant="h6">No Receipts Found</Typography>
                    <Typography color="text.secondary">
                        {searchTerm ? 'No receipts match your search criteria.' : 'No receipts available.'}
                    </Typography>
                    <Button variant="contained" startIcon={<Add />} onClick={handleAdd}>
                        Add New Receipt
                    </Button>
                </AdminComponents.CenteredMessage>
            )}
        </AdminComponents.ViewContainer>
    );

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={filterDrawerOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={filterDrawerOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                <AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="total">
                                            <Receipt />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{summaryData.totalReceipts}</Typography>
                                            <Typography variant="body2">Total Receipts</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="active">
                                            <AttachMoney />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">
                                                {summaryData.totalAmount.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                                            </Typography>
                                            <Typography variant="body2">Total Amount</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="active">
                                            <CheckCircle />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{summaryData.confirmedReceipts}</Typography>
                                            <Typography variant="body2">Confirmed</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="inactive">
                                            <Schedule />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{summaryData.pendingReceipts}</Typography>
                                            <Typography variant="body2">Pending Verification</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                                    <Button
                                        variant="contained"
                                        startIcon={<Add />}
                                        onClick={handleAdd}
                                    >
                                        Add Receipt
                                    </Button>
                                    {selectedIds.length > 0 && (
                                        <Button
                                            variant="outlined"
                                            color="error"
                                            startIcon={<Delete />}
                                            onClick={() => handleDelete(selectedIds)}
                                        >
                                            Delete Selected ({selectedIds.length})
                                        </Button>
                                    )}
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>

                        <AdminComponents.ControlsSection>
                            <AdminComponents.ControlsGroup>
                                <TextField
                                    variant="outlined"
                                    size="small"
                                    placeholder="Search receipts..."
                                    value={searchTerm}
                                    onChange={(e) => handleSearch(e.target.value)}
                                    slotProps={{ input: { startAdornment: <Search color="disabled" /> } }}
                                />
                                <Button
                                    variant="outlined"
                                    startIcon={<FilterAlt />}
                                    onClick={() => setFilterDrawerOpen(true)}
                                >
                                    Advanced Search
                                </Button>
                            </AdminComponents.ControlsGroup>

                            <AdminComponents.ControlsGroup>
                                <AdminComponents.StyledToggleButtonGroup
                                    size="small"
                                    value={viewMode}
                                    exclusive
                                    onChange={(_, newView) => newView && setViewMode(newView)}
                                >
                                    <ToggleButton value="grid" title="Card View">
                                        <ViewModule />Card
                                    </ToggleButton>
                                    <ToggleButton value="compact" title="Compact View">
                                        <Apps />Compact
                                    </ToggleButton>
                                    <ToggleButton value="list" title="List View">
                                        <ViewList />List
                                    </ToggleButton>
                                    <ToggleButton value="table" title="Table View">
                                        <GridView />Table
                                    </ToggleButton>
                                </AdminComponents.StyledToggleButtonGroup>
                            </AdminComponents.ControlsGroup>
                        </AdminComponents.ControlsSection>

                        <AdminComponents.ContentBody>
                            <AdminComponents.MainLeftPane>
                                {renderContent()}
                            </AdminComponents.MainLeftPane>
                        </AdminComponents.ContentBody>
                    </AdminComponents.MainContentArea>
                </AdminComponents.AppBody>

                {/* Filter Drawer */}
                <Drawer
                    variant="persistent"
                    anchor="right"
                    open={filterDrawerOpen}
                >
                    <AdminComponents.SidebarContainer>
                        <AdminComponents.SidebarHeader>
                            <Typography variant="h6">Advanced Search</Typography>
                            <IconButton onClick={() => setFilterDrawerOpen(false)}>
                                <Close />
                            </IconButton>
                        </AdminComponents.SidebarHeader>
                        <AdminComponents.SidebarContent>
                            <AdminComponents.SidebarSection>
                                <Typography variant="body2" color="text.secondary">
                                    Advanced filtering options will be available here.
                                </Typography>
                            </AdminComponents.SidebarSection>
                        </AdminComponents.SidebarContent>
                        <AdminComponents.SidebarFooter>
                            <Button variant="contained" fullWidth onClick={() => setFilterDrawerOpen(false)}>
                                Close
                            </Button>
                        </AdminComponents.SidebarFooter>
                    </AdminComponents.SidebarContainer>
                </Drawer>
            </AdminComponents.AppContainer>
        </ThemeProvider>
    );
};

export default Receipts;