import React, { useState, useMemo, useRef, useEffect } from 'react';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    ToggleButton, FormControlLabel, Drawer, Chip, FormControl, InputLabel, Select, MenuItem,
    Tabs, Tab, Grid, Dialog, DialogContent, DialogActions
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, People, CheckCircle, Cancel, WarningAmber, ShowChart, PieChart, DonutLarge,
    Receipt, Payment, Settings, Save, Close, Upload, CloudUpload, AccountBalance, Assessment,
    Description, Download, Print, AttachMoney
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';
import Chart from 'chart.js/auto';

// Mock receipt data based on paid invoices
const initialReceipts = [
  {
    id: 'rec-001',
    receiptNumber: 'REC-2025-001',
    invoiceNumber: 'INV-2025-001',
    accountName: 'Volvo',
    parentCompanyName: 'Reman',
    productName: 'HCL Commerce Suite',
    receiptDate: '2025-04-20',
    paymentDate: '2025-04-20',
    amount: 8333.33,
    currency: 'USD',
    amountINR: 695833.33,
    paymentMethod: 'Bank Transfer',
    transactionId: 'TXN-2025-001',
    bankReference: 'BNK-REF-001',
    status: 'Confirmed',
    region: 'North America',
    domainVertical: 'Manufacturing',
    createdBy: 'Jane Doe',
    createdDate: '2025-04-20T10:30:00Z'
  },
  {
    id: 'rec-002',
    receiptNumber: 'REC-2025-002',
    invoiceNumber: 'INV-2025-003',
    accountName: 'Prevost',
    parentCompanyName: 'DMS',
    productName: 'HCL Manufacturing Suite',
    receiptDate: '2025-05-15',
    paymentDate: '2025-05-15',
    amount: 10000,
    currency: 'INR',
    amountINR: 10000,
    paymentMethod: 'Credit Card',
    transactionId: 'TXN-2025-002',
    bankReference: 'CC-REF-002',
    status: 'Confirmed',
    region: 'Asia',
    domainVertical: 'Manufacturing',
    createdBy: 'Alex Turner',
    createdDate: '2025-05-15T09:00:00Z'
  },
  {
    id: 'rec-003',
    receiptNumber: 'REC-2025-003',
    invoiceNumber: 'INV-2025-004',
    accountName: 'Witgen',
    parentCompanyName: 'Lincolin',
    productName: 'HCL Energy Suite',
    receiptDate: '2025-07-20',
    paymentDate: '2025-07-20',
    amount: 9166.67,
    currency: 'EUR',
    amountINR: 826833.33,
    paymentMethod: 'Wire Transfer',
    transactionId: 'TXN-2025-003',
    bankReference: 'WIRE-REF-003',
    status: 'Pending Verification',
    region: 'Europe',
    domainVertical: 'Energy',
    createdBy: 'Maria Lopez',
    createdDate: '2025-07-20T13:00:00Z'
  },
  {
    id: 'rec-004',
    receiptNumber: 'REC-2025-004',
    invoiceNumber: 'INV-2025-005',
    accountName: 'Yanmar',
    parentCompanyName: 'DMS',
    productName: 'HCL Logistics Suite',
    receiptDate: '2025-09-15',
    paymentDate: '2025-09-15',
    amount: 8750,
    currency: 'INR',
    amountINR: 8750,
    paymentMethod: 'Online Payment',
    transactionId: 'TXN-2025-004',
    bankReference: 'ONL-REF-004',
    status: 'Confirmed',
    region: 'Asia',
    domainVertical: 'Logistics',
    createdBy: 'Emily Chen',
    createdDate: '2025-09-15T15:30:00Z'
  }
];

const ALL_COLUMNS = [
  { key: 'receiptNumber', label: 'Receipt Number', type: 'string', groupable: true },
  { key: 'invoiceNumber', label: 'Invoice Number', type: 'string', groupable: true },
  { key: 'accountName', label: 'Account Name', type: 'string', groupable: true },
  { key: 'productName', label: 'Product Name', type: 'string', groupable: true },
  { key: 'receiptDate', label: 'Receipt Date', type: 'date', groupable: true },
  { key: 'amountINR', label: 'Amount (INR)', type: 'number', groupable: false },
  { key: 'paymentMethod', label: 'Payment Method', type: 'string', groupable: true },
  { key: 'status', label: 'Status', type: 'string', groupable: true },
];

const FILTER_OPERATORS = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'];

const ActionButtons = ({ receipt, onView, onEdit, onDelete }) => (
    <Box onClick={e => e.stopPropagation()}>
        <IconButton size="small" onClick={() => onView(receipt)} title="View Details">
            <Visibility fontSize="small" />
        </IconButton>
        <IconButton size="small" onClick={() => onEdit(receipt)} title="Edit">
            <Edit fontSize="small" />
        </IconButton>
        <IconButton size="small" color="error" onClick={() => onDelete([receipt.id])} title="Delete">
            <Delete fontSize="small" />
        </IconButton>
        <IconButton size="small" onClick={() => {
            // Create a printable receipt format
            const printContent = `
                <div style="font-family: Arial, sans-serif; padding: 20px;">
                    <h2>Receipt: ${receipt.receiptNumber}</h2>
                    <hr>
                    <p><strong>Invoice Number:</strong> ${receipt.invoiceNumber}</p>
                    <p><strong>Account Name:</strong> ${receipt.accountName}</p>
                    <p><strong>Product:</strong> ${receipt.productName}</p>
                    <p><strong>Receipt Date:</strong> ${new Date(receipt.receiptDate).toLocaleDateString('en-IN')}</p>
                    <p><strong>Payment Date:</strong> ${new Date(receipt.paymentDate).toLocaleDateString('en-IN')}</p>
                    <p><strong>Amount:</strong> ${receipt.amountINR.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}</p>
                    <p><strong>Payment Method:</strong> ${receipt.paymentMethod}</p>
                    <p><strong>Transaction ID:</strong> ${receipt.transactionId}</p>
                    <p><strong>Status:</strong> ${receipt.status}</p>
                    <hr>
                    <p style="text-align: center; margin-top: 30px;">Thank you for your payment!</p>
                </div>
            `;
            const printWindow = window.open('', '_blank');
            if (printWindow) {
                printWindow.document.body.innerHTML = printContent;
                printWindow.print();
                printWindow.close();
            }
        }} title="Print Receipt">
            <Print fontSize="small" />
        </IconButton>
    </Box>
);

const ReceiptCard = ({ receipt, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
  <AdminComponents.CardBase isSelected={isSelected}>
    <Checkbox
      className="card-checkbox"
      checked={isChecked}
      onChange={() => onSelect(receipt.id)}
      onClick={e => e.stopPropagation()}
    />
    <AdminComponents.CardActionContainer>
      <ActionButtons receipt={receipt} onView={onView} onEdit={onEdit} onDelete={onDelete} />
    </AdminComponents.CardActionContainer>
    <AdminComponents.PaddedCardContent>
      <Typography variant="h6" component="div" noWrap>{receipt.receiptNumber}</Typography>
      <Typography color="text.secondary" noWrap gutterBottom>{receipt.accountName}</Typography>
      <AdminComponents.StatusBadge
        ownerState={{ status: receipt.status }}
        label={receipt.status}
        size="small"
      />
      <AdminComponents.CardDivider />
      <AdminComponents.CardDetailsGrid>
        <AdminComponents.CardDetailLabel variant="body2">Invoice:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>{receipt.invoiceNumber}</Typography>
        <AdminComponents.CardDetailLabel variant="body2">Product:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>{receipt.productName}</Typography>
        <AdminComponents.CardDetailLabel variant="body2">Amount:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>
          {receipt.amountINR.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
        </Typography>
        <AdminComponents.CardDetailLabel variant="body2">Payment Method:</AdminComponents.CardDetailLabel>
        <Typography variant="body2" noWrap>{receipt.paymentMethod}</Typography>
      </AdminComponents.CardDetailsGrid>
    </AdminComponents.PaddedCardContent>
  </AdminComponents.CardBase>
);

const ReceiptCompactCard = ({ receipt, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
  <AdminComponents.CardBase isSelected={isSelected}>
    <Checkbox
      className="card-checkbox"
      checked={isChecked}
      onChange={() => onSelect(receipt.id)}
      onClick={e => e.stopPropagation()}
    />
    <AdminComponents.CardActionContainer>
      <ActionButtons receipt={receipt} onView={onView} onEdit={onEdit} onDelete={onDelete} />
    </AdminComponents.CardActionContainer>
    <AdminComponents.CompactCardContent>
      <div>
        <Typography variant="subtitle1" fontWeight="bold" noWrap>{receipt.receiptNumber}</Typography>
        <Typography variant="caption" color="text.secondary">{receipt.accountName}</Typography>
      </div>
      <AdminComponents.CompactCardFooter>
        <Typography variant="body2" fontWeight="500">
          {receipt.amountINR.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
        </Typography>
        <AdminComponents.StatusBadge
          ownerState={{ status: receipt.status }}
          label={receipt.status}
          size="small"
        />
      </AdminComponents.CompactCardFooter>
    </AdminComponents.CompactCardContent>
  </AdminComponents.CardBase>
);

const ReceiptListItem = ({ receipt, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
  <AdminComponents.ListItemCard isSelected={isSelected}>
    <AdminComponents.ListItemGrid>
      <Checkbox
        checked={isChecked}
        onChange={() => onSelect(receipt.id)}
        onClick={e => e.stopPropagation()}
      />
      <Box>
        <Typography fontWeight="bold">{receipt.receiptNumber}</Typography>
        <Typography variant="body2" color="text.secondary">{receipt.accountName}</Typography>
      </Box>
      <Typography variant="body2">{receipt.invoiceNumber}</Typography>
      <Typography variant="body2">{receipt.productName}</Typography>
      <Typography variant="body2">
        {receipt.amountINR.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
      </Typography>
      <Typography variant="body2">{receipt.paymentMethod}</Typography>
      <AdminComponents.StatusBadge
        ownerState={{ status: receipt.status }}
        label={receipt.status}
        size="small"
      />
      <AdminComponents.ListItemActions>
        <ActionButtons receipt={receipt} onView={onView} onEdit={onEdit} onDelete={onDelete} />
      </AdminComponents.ListItemActions>
    </AdminComponents.ListItemGrid>
  </AdminComponents.ListItemCard>
);

const ReceiptTable = ({
  receipts,
  onRowClick,
  onHeaderClick,
  sortColumn,
  sortDirection,
  selectedId,
  selectedIds,
  onSelectAll,
  onSelectOne,
  columnOrder,
  setColumnOrder,
  onDelete,
  onEdit,
  onView
}) => {
    const dragItemIndex = React.useRef(null);
    const dragOverItemIndex = React.useRef(null);

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };

    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox">
                            <Checkbox
                                indeterminate={selectedIds.length > 0 && selectedIds.length < receipts.length}
                                checked={receipts.length > 0 && selectedIds.length === receipts.length}
                                onChange={onSelectAll}
                            />
                        </TableCell>
                        {columnOrder.map((colKey, idx) => (
                            <TableCell
                                key={colKey}
                                draggable
                                onDragStart={() => { dragItemIndex.current = idx; }}
                                onDragEnter={() => { dragOverItemIndex.current = idx; }}
                                onDragEnd={handleDrop}
                                onDragOver={e => e.preventDefault()}
                                style={{ cursor: 'move' }}
                            >
                                <TableSortLabel
                                    active={sortColumn === colKey}
                                    direction={sortDirection}
                                    onClick={() => onHeaderClick(colKey)}
                                >
                                    {ALL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </TableCell>
                        ))}
                        <TableCell align="center">Actions</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {receipts.map(receipt => (
                        <TableRow
                            key={receipt.id}
                            hover
                            selected={selectedId === receipt.id}
                            onClick={() => onRowClick(receipt)}
                        >
                            <TableCell padding="checkbox">
                                <Checkbox
                                    checked={selectedIds.includes(receipt.id)}
                                    onChange={() => onSelectOne(receipt.id)}
                                    onClick={e => e.stopPropagation()}
                                />
                            </TableCell>
                            {columnOrder.map(colKey => (
                                <TableCell key={colKey}>
                                    {colKey === 'amountINR'
                                        ? receipt[colKey].toLocaleString('en-IN', { style: 'currency', currency: 'INR' })
                                        : colKey === 'receiptDate'
                                        ? new Date(receipt[colKey]).toLocaleDateString('en-IN')
                                        : receipt[colKey]}
                                </TableCell>
                            ))}
                            <TableCell align="center">
                                <ActionButtons receipt={receipt} onView={onView} onEdit={onEdit} onDelete={onDelete} />
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </AdminComponents.TableViewContainer>
    );
};



// Receipt Dialog Component
const ReceiptDialog = ({ open, onClose, receiptData, mode, onSave }) => {
  const [formData, setFormData] = useState({
    receiptNumber: '',
    invoiceNumber: '',
    accountName: '',
    productName: '',
    receiptDate: '',
    paymentDate: '',
    amount: '',
    currency: 'INR',
    amountINR: '',
    paymentMethod: 'Bank Transfer',
    transactionId: '',
    bankReference: '',
    status: 'Confirmed',
    region: '',
    domainVertical: ''
  });

  // Initialize form data when dialog opens
  React.useEffect(() => {
    if (open) {
      if (receiptData && (mode === 'edit' || mode === 'view')) {
        setFormData(receiptData);
      } else {
        // Reset form for add mode
        setFormData({
          receiptNumber: `REC-${new Date().getFullYear()}-${String(Date.now()).slice(-3)}`,
          invoiceNumber: '',
          accountName: '',
          productName: '',
          receiptDate: new Date().toISOString().split('T')[0],
          paymentDate: new Date().toISOString().split('T')[0],
          amount: '',
          currency: 'INR',
          amountINR: '',
          paymentMethod: 'Bank Transfer',
          transactionId: '',
          bankReference: '',
          status: 'Confirmed',
          region: '',
          domainVertical: ''
        });
      }
    }
  }, [open, receiptData, mode]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    if (mode !== 'view') {
      // Basic validation
      if (!formData.receiptNumber || !formData.invoiceNumber || !formData.accountName || !formData.amountINR) {
        alert('Please fill in all required fields');
        return;
      }
      onSave(formData);
    }
  };

  const isReadOnly = mode === 'view';
  const dialogTitle = mode === 'add' ? 'Add New Receipt' : mode === 'edit' ? 'Edit Receipt' : 'View Receipt';

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="md">
      <AdminComponents.DialogHeader>
        <Typography variant="h6">{dialogTitle}</Typography>
        <IconButton onClick={onClose} size="small">
          <Close />
        </IconButton>
      </AdminComponents.DialogHeader>
      <DialogContent>
        <Box sx={{ p: 2 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Receipt Number"
                value={formData.receiptNumber}
                onChange={(e) => handleInputChange('receiptNumber', e.target.value)}
                disabled={isReadOnly}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Invoice Number"
                value={formData.invoiceNumber}
                onChange={(e) => handleInputChange('invoiceNumber', e.target.value)}
                disabled={isReadOnly}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Account Name"
                value={formData.accountName}
                onChange={(e) => handleInputChange('accountName', e.target.value)}
                disabled={isReadOnly}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Product Name"
                value={formData.productName}
                onChange={(e) => handleInputChange('productName', e.target.value)}
                disabled={isReadOnly}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Receipt Date"
                type="date"
                value={formData.receiptDate}
                onChange={(e) => handleInputChange('receiptDate', e.target.value)}
                disabled={isReadOnly}
                slotProps={{ inputLabel: { shrink: true } }}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Payment Date"
                type="date"
                value={formData.paymentDate}
                onChange={(e) => handleInputChange('paymentDate', e.target.value)}
                disabled={isReadOnly}
                slotProps={{ inputLabel: { shrink: true } }}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Amount (INR)"
                type="number"
                value={formData.amountINR}
                onChange={(e) => handleInputChange('amountINR', parseFloat(e.target.value) || 0)}
                disabled={isReadOnly}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth disabled={isReadOnly}>
                <InputLabel>Payment Method</InputLabel>
                <Select
                  value={formData.paymentMethod}
                  label="Payment Method"
                  onChange={(e) => handleInputChange('paymentMethod', e.target.value)}
                >
                  <MenuItem value="Bank Transfer">Bank Transfer</MenuItem>
                  <MenuItem value="Credit Card">Credit Card</MenuItem>
                  <MenuItem value="Wire Transfer">Wire Transfer</MenuItem>
                  <MenuItem value="Online Payment">Online Payment</MenuItem>
                  <MenuItem value="Cash">Cash</MenuItem>
                  <MenuItem value="Cheque">Cheque</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Transaction ID"
                value={formData.transactionId}
                onChange={(e) => handleInputChange('transactionId', e.target.value)}
                disabled={isReadOnly}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Bank Reference"
                value={formData.bankReference}
                onChange={(e) => handleInputChange('bankReference', e.target.value)}
                disabled={isReadOnly}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth disabled={isReadOnly}>
                <InputLabel>Status</InputLabel>
                <Select
                  value={formData.status}
                  label="Status"
                  onChange={(e) => handleInputChange('status', e.target.value)}
                >
                  <MenuItem value="Confirmed">Confirmed</MenuItem>
                  <MenuItem value="Pending Verification">Pending Verification</MenuItem>
                  <MenuItem value="Cancelled">Cancelled</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Region"
                value={formData.region}
                onChange={(e) => handleInputChange('region', e.target.value)}
                disabled={isReadOnly}
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
      <DialogActions sx={{ p: 2, borderTop: '1px solid var(--border-color)' }}>
        <Button onClick={onClose} variant="outlined" startIcon={<Close />}>
          {mode === 'view' ? 'Close' : 'Cancel'}
        </Button>
        {mode !== 'view' && (
          <Button onClick={handleSave} variant="contained" startIcon={<Save />}>
            {mode === 'add' ? 'Add Receipt' : 'Save Changes'}
          </Button>
        )}
        {mode === 'view' && (
          <Button onClick={() => window.print()} variant="outlined" startIcon={<Print />}>
            Print Receipt
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

// ReceiptsGraph component (modeled after CustomerGraph)
const ReceiptsGraph = ({ receipt, chartType, allReceipts }) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  useEffect(() => {
    if (chartInstance.current) chartInstance.current.destroy();
    let chartData, chartLabels, chartTitle;
    if (receipt) {
      chartLabels = ['Confirmed', 'Pending Verification'];
      chartData = [receipt.status === 'Confirmed' ? 1 : 0, receipt.status === 'Pending Verification' ? 1 : 0];
      chartTitle = `${receipt.accountName} - Receipt Analysis`;
    } else if (allReceipts && allReceipts.length > 0) {
      // Show summary chart for all receipts
      const statusCounts = allReceipts.reduce((acc, r) => {
        acc[r.status] = (acc[r.status] || 0) + 1;
        return acc;
      }, {});
      chartLabels = Object.keys(statusCounts);
      chartData = chartLabels.map(label => statusCounts[label]);
      chartTitle = 'All Receipts - Status Distribution';
    }
    if (chartRef.current && (receipt || (allReceipts && allReceipts.length > 0))) {
      const ctx = chartRef.current.getContext('2d');
      chartInstance.current = new Chart(ctx, {
        type: chartType,
        data: {
          labels: chartLabels,
          datasets: [{
            label: 'Receipt Status',
            data: chartData,
            backgroundColor: ['pie', 'doughnut'].includes(chartType) ? theme.palette.chart?.backgrounds || [theme.palette.primary.main, theme.palette.secondary.main] : theme.palette.primary.main,
            borderColor: theme.palette.primary.dark,
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: { title: { display: true, text: chartTitle } },
        }
      });
    }
    return () => { if (chartInstance.current) chartInstance.current.destroy(); };
  }, [receipt, chartType, allReceipts]);

  return (
    <>
      {(receipt || (allReceipts && allReceipts.length > 0)) ? (
        <AdminComponents.GraphCanvasContainer>
          <canvas ref={chartRef}></canvas>
        </AdminComponents.GraphCanvasContainer>
      ) : (
        <AdminComponents.CenteredMessage>
          <Typography>Select a receipt to see graph</Typography>
        </AdminComponents.CenteredMessage>
      )}
    </>
  );
};

const Receipts = () => {
    const [receipts, setReceipts] = useState(initialReceipts);
    const [filteredReceipts, setFilteredReceipts] = useState(initialReceipts);
    const [selectedId, setSelectedId] = useState(null);
    const [selectedIds, setSelectedIds] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [sortColumn, setSortColumn] = useState('receiptDate');
    const [sortDirection, setSortDirection] = useState('desc');
    const [viewMode, setViewMode] = useState('table');
    const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(col => col.key));
    const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
    const [isGraphVisible, setIsGraphVisible] = useState(false);
    const [chartType, setChartType] = useState('bar');

    // Dialog states
    const [dialogOpen, setDialogOpen] = useState(false);
    const [dialogMode, setDialogMode] = useState('add'); // 'add', 'edit', 'view'
    const [dialogReceipt, setDialogReceipt] = useState(null);

    // Success message state
    const [successMessage, setSuccessMessage] = useState('');

    // Add state for sidebar, filters, and group by
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [sidebarMode, setSidebarMode] = useState('search');
    const [stagedFilters, setStagedFilters] = useState([]);
    const [activeFilters, setActiveFilters] = useState([]);
    const [filterBuilder, setFilterBuilder] = useState({ field: '', operator: '', value: '' });
    const [groupByKeys, setGroupByKeys] = useState([]);

    const handleGroupByChange = (key) => {
      setGroupByKeys(prev =>
        prev.includes(key) ? prev.filter(k => k !== key) : [...prev, key]
      );
    };

    // Filter and search logic
    const handleSearch = (term) => {
        setSearchTerm(term);
        if (!term.trim()) {
            setFilteredReceipts(receipts);
            return;
        }

        const filtered = receipts.filter(receipt =>
            Object.values(receipt).some(value =>
                value && value.toString().toLowerCase().includes(term.toLowerCase())
            )
        );
        setFilteredReceipts(filtered);
    };

    // Sorting logic
    const handleSort = (column) => {
        const isAsc = sortColumn === column && sortDirection === 'asc';
        const direction = isAsc ? 'desc' : 'asc';
        setSortColumn(column);
        setSortDirection(direction);

        const sorted = [...filteredReceipts].sort((a, b) => {
            if (column === 'amountINR') {
                return direction === 'asc' ? a[column] - b[column] : b[column] - a[column];
            }
            if (column === 'receiptDate') {
                return direction === 'asc'
                    ? new Date(a[column]) - new Date(b[column])
                    : new Date(b[column]) - new Date(a[column]);
            }
            const aVal = a[column]?.toString().toLowerCase() || '';
            const bVal = b[column]?.toString().toLowerCase() || '';
            return direction === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
        });

        setFilteredReceipts(sorted);
    };

    // Selection handlers
    const handleSelectAll = (event) => {
        if (event.target.checked) {
            setSelectedIds(filteredReceipts.map(receipt => receipt.id));
        } else {
            setSelectedIds([]);
        }
    };

    const handleSelectOne = (id) => {
        setSelectedIds(prev =>
            prev.includes(id) ? prev.filter(selectedId => selectedId !== id) : [...prev, id]
        );
    };

    const handleRowClick = (receipt) => {
        setSelectedId(receipt.id);
    };

    // Dialog handlers
    const handleOpenDialog = (mode, receipt = null) => {
        setDialogMode(mode);
        setDialogReceipt(receipt);
        setDialogOpen(true);
    };

    const handleCloseDialog = () => {
        setDialogOpen(false);
        setDialogMode('add');
        setDialogReceipt(null);
    };

    const handleSaveDialog = (receiptData) => {
        if (dialogMode === 'add') {
            const newReceipt = {
                ...receiptData,
                id: `rec-${Date.now()}`,
                createdDate: new Date().toISOString(),
                createdBy: 'Current User'
            };
            setReceipts(prev => [...prev, newReceipt]);
            setFilteredReceipts(prev => [...prev, newReceipt]);
            setSuccessMessage('Receipt added successfully!');
        } else if (dialogMode === 'edit') {
            setReceipts(prev => prev.map(r => r.id === receiptData.id ? receiptData : r));
            setFilteredReceipts(prev => prev.map(r => r.id === receiptData.id ? receiptData : r));
            setSuccessMessage('Receipt updated successfully!');
        }
        handleCloseDialog();

        // Clear success message after 3 seconds
        setTimeout(() => setSuccessMessage(''), 3000);
    };

    // Action handlers
    const handleView = (receipt) => {
        handleOpenDialog('view', receipt);
    };

    const handleEdit = (receipt) => {
        handleOpenDialog('edit', receipt);
    };

    const handleDelete = (ids) => {
        if (window.confirm(`Are you sure you want to delete ${ids.length} receipt(s)?`)) {
            setReceipts(prev => prev.filter(receipt => !ids.includes(receipt.id)));
            setFilteredReceipts(prev => prev.filter(receipt => !ids.includes(receipt.id)));
            setSelectedIds([]);
            setSelectedId(null);
        }
    };

    const handleAdd = () => {
        handleOpenDialog('add');
    };

    // Summary calculations
    const summaryData = useMemo(() => {
        const totalReceipts = filteredReceipts.length;
        const totalAmount = filteredReceipts.reduce((sum, receipt) => sum + receipt.amountINR, 0);
        const confirmedReceipts = filteredReceipts.filter(r => r.status === 'Confirmed').length;
        const pendingReceipts = filteredReceipts.filter(r => r.status === 'Pending Verification').length;

        return {
            totalReceipts,
            totalAmount,
            confirmedReceipts,
            pendingReceipts,
            averageAmount: totalReceipts > 0 ? totalAmount / totalReceipts : 0
        };
    }, [filteredReceipts]);

    const renderContent = () => (
        <AdminComponents.ViewContainer>
            {filteredReceipts.length > 0 ? (
                <>
                    {viewMode === 'grid' && (
                        <AdminComponents.GridView>
                            {filteredReceipts.map(receipt => (
                                <ReceiptCard
                                    key={receipt.id}
                                    receipt={receipt}
                                    isSelected={selectedId === receipt.id}
                                    onSelect={handleRowClick}
                                    isChecked={selectedIds.includes(receipt.id)}
                                    onDelete={handleDelete}
                                    onEdit={handleEdit}
                                    onView={handleView}
                                />
                            ))}
                        </AdminComponents.GridView>
                    )}
                    {viewMode === 'compact' && (
                        <AdminComponents.CompactView>
                            {filteredReceipts.map(receipt => (
                                <ReceiptCompactCard
                                    key={receipt.id}
                                    receipt={receipt}
                                    isSelected={selectedId === receipt.id}
                                    onSelect={handleRowClick}
                                    isChecked={selectedIds.includes(receipt.id)}
                                    onDelete={handleDelete}
                                    onEdit={handleEdit}
                                    onView={handleView}
                                />
                            ))}
                        </AdminComponents.CompactView>
                    )}
                    {viewMode === 'list' && (
                        <AdminComponents.ListView>
                            {filteredReceipts.map(receipt => (
                                <ReceiptListItem
                                    key={receipt.id}
                                    receipt={receipt}
                                    isSelected={selectedId === receipt.id}
                                    onSelect={handleRowClick}
                                    isChecked={selectedIds.includes(receipt.id)}
                                    onDelete={handleDelete}
                                    onEdit={handleEdit}
                                    onView={handleView}
                                />
                            ))}
                        </AdminComponents.ListView>
                    )}
                    {viewMode === 'table' && (
                        <ReceiptTable
                            receipts={filteredReceipts}
                            onRowClick={handleRowClick}
                            onHeaderClick={handleSort}
                            sortColumn={sortColumn}
                            sortDirection={sortDirection}
                            selectedId={selectedId}
                            selectedIds={selectedIds}
                            onSelectAll={handleSelectAll}
                            onSelectOne={handleSelectOne}
                            columnOrder={columnOrder}
                            setColumnOrder={setColumnOrder}
                            onDelete={handleDelete}
                            onEdit={handleEdit}
                            onView={handleView}
                        />
                    )}
                </>
            ) : (
                <AdminComponents.CenteredMessage component={Paper}>
                    <AdminComponents.LargeIcon color="disabled" />
                    <Typography variant="h6">No Receipts Found</Typography>
                    <Typography color="text.secondary">
                        {searchTerm ? 'No receipts match your search criteria.' : 'No receipts available.'}
                    </Typography>
                    <Button variant="contained" startIcon={<Add />} onClick={handleAdd}>
                        Add New Receipt
                    </Button>
                </AdminComponents.CenteredMessage>
            )}
        </AdminComponents.ViewContainer>
    );

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={isSidebarOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={isSidebarOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                {successMessage && (
                                    <Box sx={{ mb: 2 }}>
                                        <Typography
                                            variant="body2"
                                            sx={{
                                                color: 'var(--success-main)',
                                                backgroundColor: 'var(--success-light)',
                                                padding: '8px 16px',
                                                borderRadius: 'var(--radius-md)',
                                                border: '1px solid var(--success-main)'
                                            }}
                                        >
                                            {successMessage}
                                        </Typography>
                                    </Box>
                                )}
                                <AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="total">
                                            <Receipt />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{summaryData.totalReceipts}</Typography>
                                            <Typography variant="body2">Total Receipts</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="active">
                                            <AttachMoney />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">
                                                {summaryData.totalAmount.toLocaleString('en-IN', { style: 'currency', currency: 'INR' })}
                                            </Typography>
                                            <Typography variant="body2">Total Amount</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="active">
                                            <CheckCircle />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{summaryData.confirmedReceipts}</Typography>
                                            <Typography variant="body2">Confirmed</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryAvatar variant="inactive">
                                            <Schedule />
                                        </AdminComponents.SummaryAvatar>
                                        <Box>
                                            <Typography variant="h6">{summaryData.pendingReceipts}</Typography>
                                            <Typography variant="body2">Pending Verification</Typography>
                                        </Box>
                                    </AdminComponents.SummaryCard>
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                                    <Button
                                        variant="contained"
                                        startIcon={<Add />}
                                        onClick={handleAdd}
                                    >
                                        Add Receipt
                                    </Button>
                                    {selectedIds.length > 0 && (
                                        <Button
                                            variant="outlined"
                                            color="error"
                                            startIcon={<Delete />}
                                            onClick={() => handleDelete(selectedIds)}
                                        >
                                            Delete Selected ({selectedIds.length})
                                        </Button>
                                    )}
                                    <Button
                                        variant="outlined"
                                        startIcon={<BarChart />}
                                        onClick={() => setIsGraphVisible(v => !v)}
                                    >
                                        Graphs
                                    </Button>
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>

                        <AdminComponents.ControlsSection>
                            <AdminComponents.ControlsGroup>
                                <TextField
                                    variant="outlined"
                                    size="small"
                                    placeholder="Search receipts..."
                                    value={searchTerm}
                                    onChange={(e) => handleSearch(e.target.value)}
                                    slotProps={{ input: { startAdornment: <Search color="disabled" /> } }}
                                />
                            </AdminComponents.ControlsGroup>
                            <AdminComponents.ControlsGroup>
                                <Button
                                    variant="outlined"
                                    startIcon={<FilterAlt />}
                                    onClick={() => { setIsSidebarOpen(true); setSidebarMode('search'); }}
                                    sx={{ mr: 2 }}
                                >
                                    Advanced Search
                                </Button>
                                <Button
                                    variant="outlined"
                                    startIcon={<Settings />}
                                    onClick={() => { setIsSidebarOpen(true); setSidebarMode('grid'); }}
                                    sx={{ mr: 2 }}
                                >
                                    Table Settings
                                </Button>
                                <AdminComponents.StyledToggleButtonGroup
                                    size="small"
                                    value={viewMode}
                                    exclusive
                                    onChange={(_, newView) => newView && setViewMode(newView)}
                                >
                                    <ToggleButton value="grid" title="Card View">
                                        <ViewModule />Card
                                    </ToggleButton>
                                    <ToggleButton value="compact" title="Compact View">
                                        <Apps />Compact
                                    </ToggleButton>
                                    <ToggleButton value="list" title="List View">
                                        <ViewList />List
                                    </ToggleButton>
                                    <ToggleButton value="table" title="Table View">
                                        <GridView />Table
                                    </ToggleButton>
                                </AdminComponents.StyledToggleButtonGroup>
                            </AdminComponents.ControlsGroup>
                        </AdminComponents.ControlsSection>

                        <AdminComponents.ContentBody>
                            <AdminComponents.MainLeftPane>
                                {renderContent()}
                            </AdminComponents.MainLeftPane>
                            <AdminComponents.DetailsPane isCollapsed={!isGraphVisible}>
                                <AdminComponents.ChartTypeSelectorContainer>
                                    <AdminComponents.StyledToggleButtonGroup
                                        value={chartType}
                                        exclusive
                                        onChange={(e, newType) => newType && setChartType(newType)}
                                        size="small"
                                        fullWidth
                                    >
                                        <ToggleButton value="bar" title="Bar Chart"><BarChart />Bar</ToggleButton>
                                        <ToggleButton value="line" title="Line Chart"><ShowChart />Line</ToggleButton>
                                        <ToggleButton value="pie" title="Pie Chart"><PieChart />Pie</ToggleButton>
                                        <ToggleButton value="doughnut" title="Doughnut Chart"><DonutLarge />Donut</ToggleButton>
                                    </AdminComponents.StyledToggleButtonGroup>
                                </AdminComponents.ChartTypeSelectorContainer>
                                <ReceiptsGraph receipt={filteredReceipts.find(r => r.id === selectedId)} chartType={chartType} allReceipts={filteredReceipts} />
                            </AdminComponents.DetailsPane>
                        </AdminComponents.ContentBody>
                    </AdminComponents.MainContentArea>
                </AdminComponents.AppBody>

                {/* Filter Drawer */}
                <Drawer
                  variant="persistent"
                  anchor="right"
                  open={isSidebarOpen}
                >
                  <AdminComponents.SidebarContainer>
                    <AdminComponents.SidebarHeader>
                      <Typography variant="h6">
                        {sidebarMode === 'search' ? 'Advanced Search' : 'Table Settings'}
                      </Typography>
                      <IconButton onClick={() => setIsSidebarOpen(false)}>
                        <Close />
                      </IconButton>
                    </AdminComponents.SidebarHeader>
                    <AdminComponents.SidebarContent>
                      {sidebarMode === 'search' && (
                        <>
                          <AdminComponents.SidebarSection>
                            <AdminComponents.SidebarSectionTitle>Filter Builder</AdminComponents.SidebarSectionTitle>
                            <FormControl fullWidth size="small">
                              <InputLabel>Field</InputLabel>
                              <Select value={filterBuilder.field} label="Field" onChange={e => setFilterBuilder(prev => ({ ...prev, field: e.target.value }))}>
                                {ALL_COLUMNS.map(col => <MenuItem key={col.key} value={col.key}>{col.label}</MenuItem>)}
                              </Select>
                            </FormControl>
                            <FormControl fullWidth size="small">
                              <InputLabel>Operator</InputLabel>
                              <Select value={filterBuilder.operator} label="Operator" onChange={e => setFilterBuilder(prev => ({ ...prev, operator: e.target.value }))}>
                                {FILTER_OPERATORS.map(op => <MenuItem key={op} value={op}>{op}</MenuItem>)}
                              </Select>
                            </FormControl>
                            <TextField label="Value" variant="outlined" size="small" fullWidth value={filterBuilder.value} onChange={e => setFilterBuilder(prev => ({ ...prev, value: e.target.value }))} />
                            <Button variant="outlined" fullWidth onClick={() => {
                              if (filterBuilder.field && filterBuilder.operator && filterBuilder.value) {
                                setStagedFilters([...stagedFilters, { ...filterBuilder, id: Date.now() }]);
                                setFilterBuilder({ field: '', operator: '', value: '' });
                              }
                            }}>Add Filter</Button>
                          </AdminComponents.SidebarSection>
                          <AdminComponents.SidebarSection>
                            <AdminComponents.SidebarSectionTitle>Staged Filters</AdminComponents.SidebarSectionTitle>
                            <AdminComponents.FilterChipContainer>
                              {stagedFilters.length > 0 ? stagedFilters.map(f => (
                                <Chip key={f.id} label={`${ALL_COLUMNS.find(c => c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setStagedFilters(stagedFilters.filter(sf => sf.id !== f.id))} />
                              )) : <Typography variant="body2" color="text.secondary">No filters staged.</Typography>}
                            </AdminComponents.FilterChipContainer>
                          </AdminComponents.SidebarSection>
                          <AdminComponents.SidebarSection>
                            <AdminComponents.SidebarSectionTitle>Active Filters</AdminComponents.SidebarSectionTitle>
                            <AdminComponents.FilterChipContainer>
                              {activeFilters.length > 0 ? activeFilters.map(f => (
                                <Chip key={f.id} label={`${ALL_COLUMNS.find(c => c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setActiveFilters(activeFilters.filter(af => af.id !== f.id))} />
                              )) : <Typography variant="body2" color="text.secondary">No filters active.</Typography>}
                            </AdminComponents.FilterChipContainer>
                          </AdminComponents.SidebarSection>
                        </>
                      )}
                      {sidebarMode === 'grid' && (
                        <>
                          <AdminComponents.SidebarSection>
                            <AdminComponents.SidebarSectionTitle>Visible Columns</AdminComponents.SidebarSectionTitle>
                            <AdminComponents.ColumnActionContainer>
                              <Button size="small" onClick={() => setColumnOrder(ALL_COLUMNS.map(c => c.key))}>Select All</Button>
                              <Button size="small" onClick={() => setColumnOrder(columnOrder.length > 1 ? [columnOrder[0]] : columnOrder)}>Deselect All</Button>
                            </AdminComponents.ColumnActionContainer>
                            <AdminComponents.ColumnVisibilityContainer>
                              {ALL_COLUMNS.map(col => (
                                <FormControlLabel
                                  key={col.key}
                                  control={<Checkbox checked={columnOrder.includes(col.key)} onChange={() => {
                                    const isVisible = columnOrder.includes(col.key);
                                    let newOrder;
                                    if (isVisible) {
                                      if (columnOrder.length > 1) {
                                        newOrder = columnOrder.filter(key => key !== col.key);
                                      } else {
                                        return;
                                      }
                                    } else {
                                      const originalKeys = ALL_COLUMNS.map(c => c.key);
                                      newOrder = originalKeys.filter(key => columnOrder.includes(key) || key === col.key);
                                    }
                                    setColumnOrder(newOrder);
                                  }} name={col.key} />}
                                  label={col.label}
                                />
                              ))}
                            </AdminComponents.ColumnVisibilityContainer>
                          </AdminComponents.SidebarSection>
                          <AdminComponents.SidebarSection>
                            <AdminComponents.SidebarSectionTitle>Group By</AdminComponents.SidebarSectionTitle>
                            <AdminComponents.FilterChipContainer>
                              {groupByKeys.length > 0 ? groupByKeys.map(key => (
                                <Chip
                                  key={key}
                                  label={ALL_COLUMNS.find(c => c.key === key)?.label}
                                  onDelete={() => handleGroupByChange(key)}
                                />
                              )) : <Typography variant="body2" color="text.secondary">None selected.</Typography>}
                            </AdminComponents.FilterChipContainer>
                            <AdminComponents.ColumnVisibilityContainer>
                              {ALL_COLUMNS.filter(c => c.groupable).map(col => (
                                <FormControlLabel
                                  key={col.key}
                                  control={
                                    <Checkbox
                                      checked={groupByKeys.includes(col.key)}
                                      onChange={() => handleGroupByChange(col.key)}
                                    />
                                  }
                                  label={col.label}
                                />
                              ))}
                            </AdminComponents.ColumnVisibilityContainer>
                          </AdminComponents.SidebarSection>
                        </>
                      )}
                    </AdminComponents.SidebarContent>
                    <AdminComponents.SidebarFooter>
                      {sidebarMode === 'search' && (
                        <>
                          <Button variant="outlined" onClick={() => { setStagedFilters([]); setActiveFilters([]); }}>Reset</Button>
                          <Button variant="contained" color="primary" onClick={() => { setActiveFilters([...activeFilters, ...stagedFilters]); setStagedFilters([]); }}>Apply</Button>
                        </>
                      )}
                      {sidebarMode === 'grid' && (
                        <Button variant="contained" fullWidth onClick={() => setIsSidebarOpen(false)}>Close</Button>
                      )}
                    </AdminComponents.SidebarFooter>
                  </AdminComponents.SidebarContainer>
                </Drawer>

                {/* Receipt Dialog */}
                <ReceiptDialog
                    open={dialogOpen}
                    onClose={handleCloseDialog}
                    receiptData={dialogReceipt}
                    mode={dialogMode}
                    onSave={handleSaveDialog}
                />
            </AdminComponents.AppContainer>
        </ThemeProvider>
    );
};

export default Receipts;