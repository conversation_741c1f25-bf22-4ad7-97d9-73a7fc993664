/* src/app.css */

/* --- Styles for the Component Showcase (Main Navigation) --- */

/* The main container for the showcase UI */
.showcase-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  box-sizing: border-box;
}

/* The main title: "Component Showcase" */
.showcase-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: #2EC0CB; /* A nice blue color */
  margin-bottom: 0.5rem;
}

/* The subtitle: "Select a page to view" */
.showcase-subtitle {
  font-size: 1.1rem;
  color: #6c757d; /* A muted gray color */
  margin-bottom: 2.5rem;
}

/* The container holding the three navigation cards */
.cards-container {
  display: flex;
  gap: 2rem;
  justify-content: center;
  flex-wrap: wrap; /* Allows cards to wrap on smaller screens */
}

/* Each individual navigation card */
.card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200px;
  height: 150px;
  padding: 1rem;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  text-decoration: none;
  color: #343a40;
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
}

/* The title inside each card */
.card-title {
  margin-top: 1rem;
  font-size: 1rem;
  font-weight: 500;
  text-align: center;
}

/* --- Icon Styles --- */

/* Placeholder styles for icons */
.icon-placeholder {
    width: 48px;
    height: 48px;
    background-color: #e0f7ff;
    border-radius: 4px;
    background-size: 60%;
    background-repeat: no-repeat;
    background-position: center;
}

/* SVG icons embedded as data URLs */
.simple-landing-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23007BFF'%3E%3Cpath d='M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8h16v10zM10 6h4v1H10z'/%3E%3C/svg%3E");
}

.complex-landing-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23007BFF'%3E%3Cpath d='M4 10h4V4H4v6zm6 10h4v-6h-4v6zM4 20h4v-6H4v6zm0-8h4V4H4v8zm6 0h4V4h-4v8zm6-6v6h4V4h-4zm-6 8h4v6h-4v-6zm6 0h4v6h-4v-6z'/%3E%3C/svg%3E");
}

.complex-dialog-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23007BFF'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z'/%3E%3C/svg%3E");
}